import { PrismaClient } from '@prisma/client'
import { hashPassword } from '../src/lib/auth'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create demo users
  const hashedPassword = await hashPassword('demo123')

  const user1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'demo1',
      password: hashedPassword,
      name: 'Demo User 1',
    },
  })

  const user2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'demo2',
      password: hashedPassword,
      name: 'Demo User 2',
    },
  })

  // Create demo groups
  const group1 = await prisma.group.create({
    data: {
      name: 'General Discussion',
      description: 'A place for general conversations and announcements',
      ownerId: user1.id,
    },
  })

  const group2 = await prisma.group.create({
    data: {
      name: 'Project Planning',
      description: 'Collaborate on project ideas and planning',
      ownerId: user1.id,
    },
  })

  // Add members to groups
  await prisma.groupMember.createMany({
    data: [
      { userId: user1.id, groupId: group1.id, role: 'OWNER' },
      { userId: user2.id, groupId: group1.id, role: 'MEMBER' },
      { userId: user1.id, groupId: group2.id, role: 'OWNER' },
      { userId: user2.id, groupId: group2.id, role: 'MEMBER' },
    ],
  })

  // Create demo messages
  await prisma.message.createMany({
    data: [
      {
        content: 'Welcome to MyBinder! This is our general discussion group.',
        authorId: user1.id,
        groupId: group1.id,
      },
      {
        content: 'Thanks for the welcome! Excited to be here.',
        authorId: user2.id,
        groupId: group1.id,
      },
      {
        content: 'Let\'s start planning our next project. What ideas do you have?',
        authorId: user1.id,
        groupId: group2.id,
      },
    ],
  })

  // Create demo notes
  const note1 = await prisma.note.create({
    data: {
      title: 'Meeting Notes - Project Kickoff',
      description: 'Notes from our project kickoff meeting',
      authorId: user1.id,
      groupId: group2.id,
    },
  })

  const note2 = await prisma.note.create({
    data: {
      title: 'Ideas and Brainstorming',
      description: 'Collection of ideas for future projects',
      authorId: user2.id,
      groupId: group1.id,
    },
  })

  // Create demo note blocks
  await prisma.noteBlock.createMany({
    data: [
      {
        type: 'HEADING_1',
        content: 'Project Kickoff Meeting',
        order: 0,
        noteId: note1.id,
        authorId: user1.id,
      },
      {
        type: 'TEXT',
        content: 'Date: Today\nAttendees: Demo User 1, Demo User 2',
        order: 1,
        noteId: note1.id,
        authorId: user1.id,
      },
      {
        type: 'HEADING_2',
        content: 'Agenda',
        order: 2,
        noteId: note1.id,
        authorId: user1.id,
      },
      {
        type: 'BULLET_LIST',
        content: '• Project overview\n• Timeline discussion\n• Resource allocation\n• Next steps',
        order: 3,
        noteId: note1.id,
        authorId: user1.id,
      },
      {
        type: 'HEADING_1',
        content: 'Brainstorming Session',
        order: 0,
        noteId: note2.id,
        authorId: user2.id,
      },
      {
        type: 'TEXT',
        content: 'Here are some ideas we discussed:',
        order: 1,
        noteId: note2.id,
        authorId: user2.id,
      },
      {
        type: 'NUMBERED_LIST',
        content: '1. Mobile app development\n2. Web platform enhancement\n3. User experience improvements\n4. Performance optimization',
        order: 2,
        noteId: note2.id,
        authorId: user2.id,
      },
    ],
  })

  console.log('✅ Database seeded successfully!')
  console.log('Demo accounts:')
  console.log('- Email: <EMAIL>, Password: demo123')
  console.log('- Email: <EMAIL>, Password: demo123')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String        @id @default(cuid())
  email       String        @unique
  username    String        @unique
  password    String
  name        String?
  avatar      String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  memberships GroupMember[]
  ownedGroups Group[]       @relation("GroupOwner")
  messages    Message[]
  noteBlocks  NoteBlock[]
  notes       Note[]

  @@map("users")
}

model Group {
  id          String        @id @default(cuid())
  name        String
  description String?
  avatar      String?
  isPrivate   Boolean       @default(false)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  ownerId     String
  members     GroupMember[]
  owner       User          @relation("GroupOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  messages    Message[]
  notes       Note[]

  @@map("groups")
}

model GroupMember {
  id       String   @id @default(cuid())
  role     Role     @default(MEMBER)
  joinedAt DateTime @default(now())
  userId   String
  groupId  String
  group    Group    @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, groupId])
  @@map("group_members")
}

model Message {
  id              String    @id @default(cuid())
  content         String
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  parentMessageId String?
  threadDepth     Int       @default(0)
  authorId        String
  groupId         String
  author          User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  group           Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
  parentMessage   Message?  @relation("MessageThread", fields: [parentMessageId], references: [id], onDelete: Cascade)
  replies         Message[] @relation("MessageThread")

  @@map("messages")
}

model Note {
  id          String      @id @default(cuid())
  title       String
  description String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  authorId    String
  groupId     String
  blocks      NoteBlock[]
  author      User        @relation(fields: [authorId], references: [id], onDelete: Cascade)
  group       Group       @relation(fields: [groupId], references: [id], onDelete: Cascade)

  @@map("notes")
}

model NoteBlock {
  id        String    @id @default(cuid())
  type      BlockType @default(TEXT)
  content   String
  order     Int
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  noteId    String
  authorId  String
  author    User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  note      Note      @relation(fields: [noteId], references: [id], onDelete: Cascade)

  @@map("note_blocks")
}

enum Role {
  OWNER
  ADMIN
  MEMBER
}

enum BlockType {
  TEXT
  HEADING_1
  HEADING_2
  HEADING_3
  BULLET_LIST
  NUMBERED_LIST
  CODE
  QUOTE
}

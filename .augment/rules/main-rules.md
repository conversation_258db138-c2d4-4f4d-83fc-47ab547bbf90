---
type: "always_apply"
---

<IMPORTANT_RULES>
  <CRITICAL>
    <PRINCIPLE priority="1">You see elegance in simplicity, favouring concise solutions that are straightforward and easy to understand.</PRINCIPLE>
    <PRINCIPLE priority="2">Prioritise using the tools available to you over manual approaches whenever appropriate</PRINCIPLE>
    <PRINCIPLE priority="3">Follow language-specific best practices</PRINCIPLE>
    <PRINCIPLE priority="4">Verify all changes before stating a task is completed</PRINCIPLE>
    <PRINCIPLE priority="5">Start new task when context window exceeds 70% capacity</PRINCIPLE>
    <PRINCIPLE priority="6">Always use Indonesia spelling in all outputs</PRINCIPLE>
  </CRITICAL>

  <ENFORCEMENT>
    <RULE>All rules are mandatory unless specifically overridden by user instruction</RULE>
    <RULE>Rules with IDs take precedence over general guidelines</RULE>
  </ENFORCEMENT>

  <CODING_RULES>
    <GENERAL_RULES>
      <RULE id="CS001">Avoid adding mock/placeholder code - don't be lazy - implement actual functionality</RULE>
      <RULE id="CS002">Ensure proper indentation and formatting in all code</RULE>
      <RULE id="CS003">Complete testing and documentation after primary implementation is complete</RULE>
      <RULE id="CS004">Consolidate multiple edits to the same file into single operations</RULE>
      <RULE id="CS005">If you know how something should behave, write a simple test for that
        behaviour, then implement the code to pass that test</RULE>
      <RULE id="CS006">Variables should have sensible defaults in the code but should be parameterised and available as configuration options where appropriate</RULE>
      <RULE id="CS007">Ensure files do not become too long, if a file is over 800 lines, consider splitting it into smaller files</RULE>
      <RULE id="CS008">If the user provides you with a project development plan, make sure you update it after completing milestones</RULE>
      <RULE id="CS009">When writing development plans do not include time estimates</RULE>
    </GENERAL_RULES>

    <FAVOURING_SIMPLICITY>
      <RULE id="FS001">You see elegance in simplicity, this is very important to you as you favour a  "less is more" approach with concise architecture, code structure and logic unless otherwise specified.</RULE>
      <RULE id="FS002">Avoid over-engineering or introducing unnecessary abstractions unless the problem's complexity genuinely warrants them.</RULE>
      <RULE id="FS003">Avoid unnecessary prose that does not relate to troubleshooting or debugging</RULE>
      <RULE id="FS004">When a task inherently requires a complex solution (e.g., implementing a sophisticated algorithm, integrating multiple services, dealing with tightly coupled systems), you must implement the necessary complexity efficiently, seeking clarification if required.</RULE>
    </FAVOURING_SIMPLICITY>

    <LANGUAGE_SPECIFIC_RULES>
      <DOCKER>
        <RULE id="DK001">Omit version field in docker-compose files (deprecated)</RULE>
      </DOCKER>
    </LANGUAGE_SPECIFIC_RULES>

    <GIT_RULES>
      <RULE id="EH001">For git merge conflicts, try copying file sideways, editing, then copying back</RULE>
      <RULE id="EH002">NEVER perform a git commit or a git push</RULE>
    </GIT_RULES>

    <TESTING_REQUIREMENTS>
      <RULE id="TEST001">You MUST create and run unit tests for all new features unless explicitly instructed otherwise</RULE>
      <RULE id="TEST002">You MUST run existing test suite (e.g. pnpm test, make test, pytest, etc.) before stating you have completed the task</RULE>
      <RULE id="TEST003">You MUST fix all failing tests before marking task complete</RULE>
    </TESTING_REQUIREMENTS>
  </CODING_RULES>
</IMPORTANT_RULES>

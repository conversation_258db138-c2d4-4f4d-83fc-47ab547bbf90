---
type: "manual"
---

We need to audit and update our project dependencies to their latest stable versions for security and stability improvements.

The tasks required are as follows:

    First start by reading the codebase and creating a mermaid diagram showing the components of the application and add it to a new file called ./DEPENDENCY_UPGRADE_PLAN.md
    Then check for outdated packages and identify any security vulnerabilities.
    Then create a concise upgrade plan in ./DEPENDENCY_UPGRADE_PLAN.md that prioritises security fixes and major version updates. Be sure that the plan has steps in each phase to verify the layout and styling remain consistent.

Note:

    Use available tools to research the latest versions.
    The plan should contain checklist of tasks to complete.
    The plan should not contain any unrelated work outside the scope of updating packages and ensuring the application works there after.

Review the code and dependencies, get the latest package versions, write the plan - then stop for me to review.

Once I approve the plan we will execute the upgrades while ensuring compatibility.

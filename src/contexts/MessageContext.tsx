'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { MessageWithAuthor } from '@/types'

interface MessageContextType {
  messages: MessageWithAuthor[]
  loading: boolean
  sending: boolean
  sendMessage: (groupId: string, content: string, parentMessageId?: string) => Promise<void>
  loadMessages: (groupId: string, page?: number) => Promise<void>
  loadThread: (groupId: string, messageId: string) => Promise<MessageWithAuthor | null>
  clearMessages: () => void
  hasMore: boolean
  currentPage: number
  replyToMessage: (groupId: string, content: string, parentMessageId: string) => Promise<void>
}

const MessageContext = createContext<MessageContextType | undefined>(undefined)

export function MessageProvider({ children }: { children: React.ReactNode }) {
  const [messages, setMessages] = useState<MessageWithAuthor[]>([])
  const [loading, setLoading] = useState(false)
  const [sending, setSending] = useState(false)
  const [hasMore, setHasMore] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [currentGroupId, setCurrentGroupId] = useState<string | null>(null)

  const loadMessages = async (groupId: string, page: number = 1) => {
    if (groupId !== currentGroupId) {
      // Reset state when switching groups
      setMessages([])
      setCurrentPage(1)
      setCurrentGroupId(groupId)
      page = 1
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/groups/${groupId}/messages?page=${page}&limit=50`)
      if (response.ok) {
        const data = await response.json()
        
        if (page === 1) {
          setMessages(data.messages)
        } else {
          // Append older messages for pagination
          setMessages(prev => [...data.messages, ...prev])
        }
        
        setHasMore(data.pagination.hasMore)
        setCurrentPage(page)
      } else {
        console.error('Failed to load messages')
      }
    } catch (error) {
      console.error('Failed to load messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const sendMessage = async (groupId: string, content: string, parentMessageId?: string) => {
    setSending(true)
    try {
      const response = await fetch(`/api/groups/${groupId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content, parentMessageId }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to send message')
      }

      const result = await response.json()

      if (parentMessageId) {
        // If this is a reply, update the parent message's replies
        setMessages(prev => prev.map(message => {
          if (message.id === parentMessageId) {
            return {
              ...message,
              replies: [...(message.replies || []), result.data],
              _count: {
                ...message._count,
                replies: (message._count?.replies || 0) + 1
              }
            }
          }
          return message
        }))
      } else {
        // Add new root message to the end of the list
        setMessages(prev => [...prev, result.data])
      }
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    } finally {
      setSending(false)
    }
  }

  const loadThread = async (groupId: string, messageId: string): Promise<MessageWithAuthor | null> => {
    try {
      const response = await fetch(`/api/groups/${groupId}/messages/${messageId}/thread`)
      if (response.ok) {
        const data = await response.json()
        return data.thread
      } else {
        console.error('Failed to load thread')
        return null
      }
    } catch (error) {
      console.error('Failed to load thread:', error)
      return null
    }
  }

  const replyToMessage = async (groupId: string, content: string, parentMessageId: string) => {
    return sendMessage(groupId, content, parentMessageId)
  }

  const clearMessages = () => {
    setMessages([])
    setCurrentPage(1)
    setHasMore(false)
    setCurrentGroupId(null)
  }

  return (
    <MessageContext.Provider value={{
      messages,
      loading,
      sending,
      sendMessage,
      loadMessages,
      loadThread,
      clearMessages,
      hasMore,
      currentPage,
      replyToMessage,
    }}>
      {children}
    </MessageContext.Provider>
  )
}

export function useMessages() {
  const context = useContext(MessageContext)
  if (context === undefined) {
    throw new Error('useMessages must be used within a MessageProvider')
  }
  return context
}

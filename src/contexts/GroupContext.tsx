'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { GroupWithMembers, CreateGroupData } from '@/types'

interface GroupContextType {
  groups: GroupWithMembers[]
  selectedGroup: GroupWithMembers | null
  loading: boolean
  createGroup: (data: CreateGroupData) => Promise<GroupWithMembers>
  updateGroup: (groupId: string, data: Partial<CreateGroupData>) => Promise<GroupWithMembers>
  deleteGroup: (groupId: string) => Promise<void>
  selectGroup: (group: GroupWithMembers | null) => void
  refreshGroups: () => Promise<void>
  addMember: (groupId: string, email: string, role?: 'MEMBER' | 'ADMIN') => Promise<void>
  joinGroup: (groupId: string) => Promise<void>
  leaveGroup: (groupId: string) => Promise<void>
  transferOwnership: (groupId: string, newOwnerId: string) => Promise<void>
  updateMemberRole: (groupId: string, memberId: string, role: 'MEMBER' | 'ADMIN') => Promise<void>
  removeMember: (groupId: string, memberId: string) => Promise<void>
}

const GroupContext = createContext<GroupContextType | undefined>(undefined)

export function GroupProvider({ children }: { children: React.ReactNode }) {
  const [groups, setGroups] = useState<GroupWithMembers[]>([])
  const [selectedGroup, setSelectedGroup] = useState<GroupWithMembers | null>(null)
  const [loading, setLoading] = useState(true)

  const refreshGroups = async () => {
    try {
      const response = await fetch('/api/groups')
      if (response.ok) {
        const data = await response.json()
        setGroups(data.groups)
      } else {
        console.error('Failed to fetch groups')
      }
    } catch (error) {
      console.error('Failed to refresh groups:', error)
    }
  }

  const createGroup = async (data: CreateGroupData): Promise<GroupWithMembers> => {
    const response = await fetch('/api/groups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create group')
    }

    const result = await response.json()
    await refreshGroups()
    return result.group
  }

  const updateGroup = async (groupId: string, data: Partial<CreateGroupData>): Promise<GroupWithMembers> => {
    const response = await fetch(`/api/groups/${groupId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update group')
    }

    const result = await response.json()
    await refreshGroups()
    
    // Update selected group if it's the one being updated
    if (selectedGroup?.id === groupId) {
      setSelectedGroup(result.group)
    }
    
    return result.group
  }

  const deleteGroup = async (groupId: string): Promise<void> => {
    const response = await fetch(`/api/groups/${groupId}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete group')
    }

    // Clear selected group if it's the one being deleted
    if (selectedGroup?.id === groupId) {
      setSelectedGroup(null)
    }
    
    await refreshGroups()
  }

  const selectGroup = (group: GroupWithMembers | null) => {
    setSelectedGroup(group)
  }

  const addMember = async (groupId: string, email: string, role: 'MEMBER' | 'ADMIN' = 'MEMBER'): Promise<void> => {
    const response = await fetch(`/api/groups/${groupId}/members`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, role }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to add member')
    }

    await refreshGroups()

    // Refresh selected group if it's the one being updated
    if (selectedGroup?.id === groupId) {
      const groupResponse = await fetch(`/api/groups/${groupId}`)
      if (groupResponse.ok) {
        const groupData = await groupResponse.json()
        setSelectedGroup(groupData.group)
      }
    }
  }

  const joinGroup = async (groupId: string): Promise<void> => {
    const response = await fetch(`/api/groups/${groupId}/join`, {
      method: 'POST',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to join group')
    }

    await refreshGroups()
  }

  const leaveGroup = async (groupId: string): Promise<void> => {
    const response = await fetch(`/api/groups/${groupId}/leave`, {
      method: 'POST',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to leave group')
    }

    // Clear selected group if it's the one being left
    if (selectedGroup?.id === groupId) {
      setSelectedGroup(null)
    }

    await refreshGroups()
  }

  const transferOwnership = async (groupId: string, newOwnerId: string): Promise<void> => {
    const response = await fetch(`/api/groups/${groupId}/transfer-ownership`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ newOwnerId }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to transfer ownership')
    }

    await refreshGroups()

    // Refresh selected group if it's the one being updated
    if (selectedGroup?.id === groupId) {
      const groupResponse = await fetch(`/api/groups/${groupId}`)
      if (groupResponse.ok) {
        const groupData = await groupResponse.json()
        setSelectedGroup(groupData.group)
      }
    }
  }

  const updateMemberRole = async (groupId: string, memberId: string, role: 'MEMBER' | 'ADMIN'): Promise<void> => {
    const response = await fetch(`/api/groups/${groupId}/members/${memberId}/role`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ role }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update member role')
    }

    await refreshGroups()

    // Refresh selected group if it's the one being updated
    if (selectedGroup?.id === groupId) {
      const groupResponse = await fetch(`/api/groups/${groupId}`)
      if (groupResponse.ok) {
        const groupData = await groupResponse.json()
        setSelectedGroup(groupData.group)
      }
    }
  }

  const removeMember = async (groupId: string, memberId: string): Promise<void> => {
    const response = await fetch(`/api/groups/${groupId}/members?memberId=${memberId}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to remove member')
    }

    await refreshGroups()

    // Refresh selected group if it's the one being updated
    if (selectedGroup?.id === groupId) {
      const groupResponse = await fetch(`/api/groups/${groupId}`)
      if (groupResponse.ok) {
        const groupData = await groupResponse.json()
        setSelectedGroup(groupData.group)
      }
    }
  }

  useEffect(() => {
    refreshGroups().finally(() => setLoading(false))
  }, [])

  return (
    <GroupContext.Provider value={{
      groups,
      selectedGroup,
      loading,
      createGroup,
      updateGroup,
      deleteGroup,
      selectGroup,
      refreshGroups,
      addMember,
      joinGroup,
      leaveGroup,
      transferOwnership,
      updateMemberRole,
      removeMember,
    }}>
      {children}
    </GroupContext.Provider>
  )
}

export function useGroups() {
  const context = useContext(GroupContext)
  if (context === undefined) {
    throw new Error('useGroups must be used within a GroupProvider')
  }
  return context
}

// Haptic feedback utilities for mobile devices

export const hapticFeedback = {
  // Light haptic feedback for button taps
  light: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10)
    }
  },

  // Medium haptic feedback for selections
  medium: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(20)
    }
  },

  // Strong haptic feedback for important actions
  strong: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([30, 10, 30])
    }
  },

  // Success feedback
  success: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([10, 5, 10])
    }
  },

  // Error feedback
  error: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([50, 20, 50, 20, 50])
    }
  }
}

// Check if haptic feedback is supported
export const isHapticSupported = (): boolean => {
  return 'vibrate' in navigator
}

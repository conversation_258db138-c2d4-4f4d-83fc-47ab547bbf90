import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const updateBlockSchema = z.object({
  content: z.string().max(5000, 'Content too long').optional(),
  type: z.enum(['TEXT', 'HEADING_1', 'HEADING_2', 'HEADING_3', 'BULLET_LIST', 'NUMBERED_LIST', 'CODE', 'QUOTE']).optional(),
})

// PUT /api/blocks/[blockId] - Update a specific block
export async function PUT(request: NextRequest, { params }: { params: Promise<{ blockId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { blockId } = await params
    const body = await request.json()
    const updateData = updateBlockSchema.parse(body)

    // Check if user has access to the block
    const block = await prisma.noteBlock.findFirst({
      where: {
        id: blockId,
        note: {
          group: {
            members: {
              some: { userId: user.id }
            }
          }
        }
      }
    })

    if (!block) {
      return NextResponse.json(
        { error: 'Block not found or access denied' },
        { status: 404 }
      )
    }

    // Update block
    const updatedBlock = await prisma.noteBlock.update({
      where: { id: blockId },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Block updated successfully',
      block: updatedBlock
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Update block error:', error)
    return NextResponse.json(
      { error: 'Failed to update block' },
      { status: 500 }
    )
  }
}

// DELETE /api/blocks/[blockId] - Delete a specific block
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ blockId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { blockId } = await params

    // Check if user has access to the block
    const block = await prisma.noteBlock.findFirst({
      where: {
        id: blockId,
        note: {
          group: {
            members: {
              some: { userId: user.id }
            }
          }
        }
      },
      include: {
        note: true
      }
    })

    if (!block) {
      return NextResponse.json(
        { error: 'Block not found or access denied' },
        { status: 404 }
      )
    }

    // Delete block
    await prisma.noteBlock.delete({
      where: { id: blockId }
    })

    // Reorder remaining blocks
    await prisma.noteBlock.updateMany({
      where: {
        noteId: block.noteId,
        order: { gt: block.order }
      },
      data: {
        order: { decrement: 1 }
      }
    })

    return NextResponse.json({
      message: 'Block deleted successfully'
    })
  } catch (error) {
    console.error('Delete block error:', error)
    return NextResponse.json(
      { error: 'Failed to delete block' },
      { status: 500 }
    )
  }
}

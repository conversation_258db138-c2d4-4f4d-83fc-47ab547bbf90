import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'

// GET /api/groups/discover - Discover public groups that user can join
export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const offset = (page - 1) * limit

    // Get user's current group memberships
    const userGroupIds = await prisma.groupMember.findMany({
      where: { userId: user.id },
      select: { groupId: true }
    })

    const userGroupIdList = userGroupIds.map(membership => membership.groupId)

    // Build search conditions
    const searchConditions = {
      isPrivate: false, // Only public groups
      id: {
        notIn: userGroupIdList // Exclude groups user is already in
      },
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { description: { contains: search, mode: 'insensitive' as const } }
        ]
      })
    }

    // Get public groups with pagination
    const [groups, totalCount] = await Promise.all([
      prisma.group.findMany({
        where: searchConditions,
        include: {
          owner: {
            select: {
              id: true,
              username: true,
              name: true,
              avatar: true,
            }
          },
          _count: {
            select: {
              members: true,
              messages: true,
              notes: true,
            }
          }
        },
        orderBy: [
          { updatedAt: 'desc' },
          { createdAt: 'desc' }
        ],
        take: limit,
        skip: offset
      }),
      prisma.group.count({
        where: searchConditions
      })
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      groups: groups.map(group => ({
        id: group.id,
        name: group.name,
        description: group.description,
        avatar: group.avatar,
        isPrivate: group.isPrivate,
        createdAt: group.createdAt,
        updatedAt: group.updatedAt,
        owner: group.owner,
        memberCount: group._count.members,
        messageCount: group._count.messages,
        noteCount: group._count.notes,
        canJoin: true // All returned groups are public and user is not a member
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      search: search || null
    })
  } catch (error) {
    console.error('Discover groups error:', error)
    return NextResponse.json(
      { error: 'Failed to discover groups' },
      { status: 500 }
    )
  }
}

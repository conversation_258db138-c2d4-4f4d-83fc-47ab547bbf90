import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'

// POST /api/groups/[groupId]/join - Join a group
export async function POST(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = await params

    // Check if group exists
    const group = await prisma.group.findUnique({
      where: { id: groupId },
      include: {
        owner: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        _count: {
          select: {
            members: true
          }
        }
      }
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 404 }
      )
    }

    // Check if user is already a member
    const existingMember = await prisma.groupMember.findFirst({
      where: {
        userId: user.id,
        groupId: groupId
      }
    })

    if (existingMember) {
      return NextResponse.json(
        { error: 'You are already a member of this group' },
        { status: 400 }
      )
    }

    // Check if group is private
    if (group.isPrivate) {
      return NextResponse.json(
        { 
          error: 'This is a private group. You need an invitation to join.',
          groupInfo: {
            id: group.id,
            name: group.name,
            isPrivate: group.isPrivate,
            memberCount: group._count.members
          }
        },
        { status: 403 }
      )
    }

    // For public groups, allow direct join
    const newMember = await prisma.groupMember.create({
      data: {
        userId: user.id,
        groupId: groupId,
        role: 'MEMBER'
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        group: {
          select: {
            id: true,
            name: true,
            description: true,
            isPrivate: true,
            avatar: true,
          }
        }
      }
    })

    // Log the join action
    console.log(`User ${user.id} (${user.username}) joined public group ${groupId} (${group.name})`)

    return NextResponse.json({
      message: 'Successfully joined the group',
      member: newMember,
      group: {
        id: group.id,
        name: group.name,
        description: group.description,
        isPrivate: group.isPrivate,
        avatar: group.avatar,
        memberCount: group._count.members + 1,
        owner: group.owner
      }
    })
  } catch (error) {
    console.error('Join group error:', error)
    return NextResponse.json(
      { error: 'Failed to join group' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const createMessageSchema = z.object({
  content: z.string().min(1, 'Message content is required').max(2000, 'Message too long'),
  parentMessageId: z.string().optional(),
})

// GET /api/groups/[groupId]/messages - Get messages for a group
export async function GET(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = await params
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = (page - 1) * limit

    // Check if user is member of the group
    const groupMember = await prisma.groupMember.findFirst({
      where: {
        groupId: groupId,
        userId: user.id
      }
    })

    if (!groupMember) {
      return NextResponse.json(
        { error: 'Access denied. You are not a member of this group.' },
        { status: 403 }
      )
    }

    // Get messages with pagination (only root messages, not replies)
    const messages = await prisma.message.findMany({
      where: {
        groupId: groupId,
        parentMessageId: null // Only get root messages
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        replies: {
          include: {
            author: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              }
            },
            _count: {
              select: {
                replies: true
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        },
        _count: {
          select: {
            replies: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    })

    // Get total count for pagination (only root messages)
    const totalMessages = await prisma.message.count({
      where: {
        groupId: groupId,
        parentMessageId: null
      }
    })

    const totalPages = Math.ceil(totalMessages / limit)
    const hasMore = page < totalPages

    return NextResponse.json({
      messages: messages.reverse(), // Reverse to show oldest first
      pagination: {
        page,
        limit,
        totalMessages,
        totalPages,
        hasMore
      }
    })
  } catch (error) {
    console.error('Get messages error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    )
  }
}

// POST /api/groups/[groupId]/messages - Send a message to a group
export async function POST(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = await params
    const body = await request.json()
    const { content, parentMessageId } = createMessageSchema.parse(body)

    // Check if user is member of the group
    const groupMember = await prisma.groupMember.findFirst({
      where: {
        groupId: groupId,
        userId: user.id
      }
    })

    if (!groupMember) {
      return NextResponse.json(
        { error: 'Access denied. You are not a member of this group.' },
        { status: 403 }
      )
    }

    // Validate parent message if this is a reply
    let threadDepth = 0
    if (parentMessageId) {
      const parentMessage = await prisma.message.findFirst({
        where: {
          id: parentMessageId,
          groupId: groupId
        }
      })

      if (!parentMessage) {
        return NextResponse.json(
          { error: 'Parent message not found' },
          { status: 404 }
        )
      }

      threadDepth = parentMessage.threadDepth + 1

      // Limit thread depth to prevent excessive nesting
      if (threadDepth > 3) {
        return NextResponse.json(
          { error: 'Maximum thread depth exceeded' },
          { status: 400 }
        )
      }
    }

    // Create message
    const message = await prisma.message.create({
      data: {
        content,
        authorId: user.id,
        groupId: groupId,
        parentMessageId,
        threadDepth
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        parentMessage: parentMessageId ? {
          include: {
            author: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              }
            }
          }
        } : false,
        _count: {
          select: {
            replies: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Message sent successfully',
      data: message
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Send message error:', error)
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const transferOwnershipSchema = z.object({
  newOwnerId: z.string().min(1, 'New owner ID is required'),
})

// POST /api/groups/[groupId]/transfer-ownership - Transfer group ownership
export async function POST(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = await params
    const body = await request.json()
    const { newOwnerId } = transferOwnershipSchema.parse(body)

    // Check if current user is the owner
    const group = await prisma.group.findFirst({
      where: {
        id: groupId,
        ownerId: user.id
      },
      include: {
        owner: {
          select: {
            id: true,
            username: true,
            name: true
          }
        }
      }
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found or you are not the owner' },
        { status: 404 }
      )
    }

    // Check if new owner is a member of the group
    const newOwnerMember = await prisma.groupMember.findFirst({
      where: {
        userId: newOwnerId,
        groupId: groupId
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!newOwnerMember) {
      return NextResponse.json(
        { error: 'New owner must be a member of the group' },
        { status: 400 }
      )
    }

    if (newOwnerId === user.id) {
      return NextResponse.json(
        { error: 'You are already the owner of this group' },
        { status: 400 }
      )
    }

    // Perform the ownership transfer in a transaction
    await prisma.$transaction(async (tx) => {
      // Update group owner
      await tx.group.update({
        where: { id: groupId },
        data: { ownerId: newOwnerId }
      })

      // Update new owner's role to OWNER
      await tx.groupMember.update({
        where: { id: newOwnerMember.id },
        data: { role: 'OWNER' }
      })

      // Update current owner's role to ADMIN
      await tx.groupMember.updateMany({
        where: {
          userId: user.id,
          groupId: groupId
        },
        data: { role: 'ADMIN' }
      })
    })

    // Log the ownership transfer
    console.log(`Ownership of group ${groupId} (${group.name}) transferred from ${user.id} (${user.username}) to ${newOwnerId} (${newOwnerMember.user.username})`)

    return NextResponse.json({
      message: 'Ownership transferred successfully',
      transfer: {
        groupId: group.id,
        groupName: group.name,
        formerOwner: {
          id: user.id,
          username: user.username,
          name: user.name
        },
        newOwner: {
          id: newOwnerMember.user.id,
          username: newOwnerMember.user.username,
          name: newOwnerMember.user.name
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Transfer ownership error:', error)
    return NextResponse.json(
      { error: 'Failed to transfer ownership' },
      { status: 500 }
    )
  }
}

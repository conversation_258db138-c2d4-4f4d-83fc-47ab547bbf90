import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'

// POST /api/groups/[groupId]/leave - Leave a group
export async function POST(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = await params

    // Check if group exists and get member info
    const groupMember = await prisma.groupMember.findFirst({
      where: {
        userId: user.id,
        groupId: groupId
      },
      include: {
        group: {
          select: {
            id: true,
            name: true,
            ownerId: true,
            _count: {
              select: {
                members: true
              }
            }
          }
        }
      }
    })

    if (!groupMember) {
      return NextResponse.json(
        { error: 'You are not a member of this group' },
        { status: 404 }
      )
    }

    // Prevent owner from leaving if there are other members
    if (groupMember.role === 'OWNER') {
      const memberCount = groupMember.group._count.members
      
      if (memberCount > 1) {
        // Check if there are other admins who can take over
        const otherAdmins = await prisma.groupMember.findMany({
          where: {
            groupId: groupId,
            userId: { not: user.id },
            role: 'ADMIN'
          },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                name: true
              }
            }
          }
        })

        if (otherAdmins.length === 0) {
          return NextResponse.json(
            { 
              error: 'As the group owner, you cannot leave while there are other members. Please transfer ownership to an admin first, or delete the group.',
              suggestion: 'Promote another member to admin first, then transfer ownership before leaving.',
              memberCount: memberCount
            },
            { status: 400 }
          )
        }

        // If there are admins, suggest transferring ownership
        return NextResponse.json(
          { 
            error: 'As the group owner, you should transfer ownership before leaving.',
            suggestion: 'Transfer ownership to one of the admins before leaving.',
            availableAdmins: otherAdmins.map(admin => ({
              id: admin.user.id,
              username: admin.user.username,
              name: admin.user.name
            })),
            memberCount: memberCount
          },
          { status: 400 }
        )
      }
    }

    // Remove the member from the group
    await prisma.groupMember.delete({
      where: {
        id: groupMember.id
      }
    })

    // Log the leave action
    console.log(`User ${user.id} (${user.username}) left group ${groupId} (${groupMember.group.name}) with role ${groupMember.role}`)

    // If owner left and was the only member, the group will be empty but still exist
    // This is intentional - empty groups can exist
    const remainingMemberCount = groupMember.group._count.members - 1

    return NextResponse.json({
      message: 'Successfully left the group',
      leftGroup: {
        id: groupMember.group.id,
        name: groupMember.group.name,
        formerRole: groupMember.role,
        remainingMembers: remainingMemberCount
      }
    })
  } catch (error) {
    console.error('Leave group error:', error)
    return NextResponse.json(
      { error: 'Failed to leave group' },
      { status: 500 }
    )
  }
}

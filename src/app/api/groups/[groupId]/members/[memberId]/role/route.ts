import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const updateRoleSchema = z.object({
  role: z.enum(['MEMBER', 'ADMIN'], {
    errorMap: () => ({ message: 'Role must be either MEMBER or ADMIN' })
  }),
})

// PUT /api/groups/[groupId]/members/[memberId]/role - Update member role
export async function PUT(
  request: NextRequest, 
  { params }: { params: Promise<{ groupId: string; memberId: string }> }
) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId, memberId } = await params
    const body = await request.json()
    const { role } = updateRoleSchema.parse(body)

    // Check if current user has permission (owner or admin)
    const currentUserMember = await prisma.groupMember.findFirst({
      where: {
        userId: user.id,
        groupId: groupId,
        role: { in: ['OWNER', 'ADMIN'] }
      },
      include: {
        group: {
          select: {
            id: true,
            name: true,
            ownerId: true
          }
        }
      }
    })

    if (!currentUserMember) {
      return NextResponse.json(
        { error: 'Insufficient permissions. Only owners and admins can manage member roles.' },
        { status: 403 }
      )
    }

    // Get the target member
    const targetMember = await prisma.groupMember.findFirst({
      where: {
        id: memberId,
        groupId: groupId
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!targetMember) {
      return NextResponse.json(
        { error: 'Member not found in this group' },
        { status: 404 }
      )
    }

    // Prevent changing owner's role
    if (targetMember.role === 'OWNER') {
      return NextResponse.json(
        { error: 'Cannot change the role of the group owner. Transfer ownership first if needed.' },
        { status: 400 }
      )
    }

    // Prevent non-owners from promoting to admin or demoting admins
    if (currentUserMember.role === 'ADMIN' && currentUserMember.group.ownerId !== user.id) {
      if (role === 'ADMIN' || targetMember.role === 'ADMIN') {
        return NextResponse.json(
          { error: 'Only group owners can promote members to admin or demote admins.' },
          { status: 403 }
        )
      }
    }

    // Prevent self-role changes (except owner)
    if (targetMember.userId === user.id && currentUserMember.group.ownerId !== user.id) {
      return NextResponse.json(
        { error: 'You cannot change your own role.' },
        { status: 400 }
      )
    }

    // Update the role
    const updatedMember = await prisma.groupMember.update({
      where: { id: memberId },
      data: { role },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            email: true
          }
        }
      }
    })

    // Log the role change
    console.log(`User ${user.id} (${user.username}) changed role of ${targetMember.user.username} from ${targetMember.role} to ${role} in group ${groupId}`)

    return NextResponse.json({
      message: `Member role updated successfully`,
      member: {
        id: updatedMember.id,
        user: updatedMember.user,
        role: updatedMember.role,
        joinedAt: updatedMember.joinedAt,
        previousRole: targetMember.role
      },
      updatedBy: {
        id: user.id,
        username: user.username,
        role: currentUserMember.role
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Update member role error:', error)
    return NextResponse.json(
      { error: 'Failed to update member role' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const addMemberSchema = z.object({
  email: z.string().email('Invalid email format'),
  role: z.enum(['MEMBER', 'ADMIN']).optional().default('MEMBER'),
})

// POST /api/groups/[groupId]/members - Add member to group
export async function POST(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = await params
    const body = await request.json()
    const { email, role } = addMemberSchema.parse(body)

    // Check if user has permission to add members (owner or admin)
    const group = await prisma.group.findFirst({
      where: {
        id: groupId,
        OR: [
          { ownerId: user.id },
          { 
            members: {
              some: { 
                userId: user.id,
                role: { in: ['OWNER', 'ADMIN'] }
              }
            }
          }
        ]
      }
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found or insufficient permissions' },
        { status: 404 }
      )
    }

    // Find user to add
    const userToAdd = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        avatar: true,
      }
    })

    if (!userToAdd) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is already a member
    const existingMember = await prisma.groupMember.findUnique({
      where: {
        userId_groupId: {
          userId: userToAdd.id,
          groupId: groupId
        }
      }
    })

    if (existingMember) {
      return NextResponse.json(
        { error: 'User is already a member of this group' },
        { status: 400 }
      )
    }

    // Add member
    const newMember = await prisma.groupMember.create({
      data: {
        userId: userToAdd.id,
        groupId: groupId,
        role: role
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Member added successfully',
      member: newMember
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Add member error:', error)
    return NextResponse.json(
      { error: 'Failed to add member' },
      { status: 500 }
    )
  }
}

// DELETE /api/groups/[groupId]/members - Remove member from group
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = await params
    const { searchParams } = new URL(request.url)
    const memberIdToRemove = searchParams.get('memberId')

    if (!memberIdToRemove) {
      return NextResponse.json(
        { error: 'Member ID is required as query parameter' },
        { status: 400 }
      )
    }

    // Check if current user has permission (owner or admin)
    const currentUserMember = await prisma.groupMember.findFirst({
      where: {
        userId: user.id,
        groupId: groupId,
        role: { in: ['OWNER', 'ADMIN'] }
      },
      include: {
        group: {
          select: {
            id: true,
            name: true,
            ownerId: true
          }
        }
      }
    })

    if (!currentUserMember) {
      return NextResponse.json(
        { error: 'Insufficient permissions. Only owners and admins can remove members.' },
        { status: 403 }
      )
    }

    // Get the target member
    const targetMember = await prisma.groupMember.findFirst({
      where: {
        id: memberIdToRemove,
        groupId: groupId
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!targetMember) {
      return NextResponse.json(
        { error: 'Member not found in this group' },
        { status: 404 }
      )
    }

    // Prevent removing the owner
    if (targetMember.role === 'OWNER') {
      return NextResponse.json(
        { error: 'Cannot remove the group owner from the group.' },
        { status: 400 }
      )
    }

    // Prevent non-owners from removing admins
    if (currentUserMember.role === 'ADMIN' && targetMember.role === 'ADMIN' && currentUserMember.group.ownerId !== user.id) {
      return NextResponse.json(
        { error: 'Only group owners can remove admins.' },
        { status: 403 }
      )
    }

    // Prevent self-removal (use leave endpoint instead)
    if (targetMember.userId === user.id) {
      return NextResponse.json(
        { error: 'Use the leave endpoint to remove yourself from the group.' },
        { status: 400 }
      )
    }

    // Remove the member
    await prisma.groupMember.delete({
      where: { id: memberIdToRemove }
    })

    // Log the removal
    console.log(`User ${user.id} (${user.username}) removed ${targetMember.user.username} (${targetMember.role}) from group ${groupId}`)

    return NextResponse.json({
      message: 'Member removed successfully',
      removedMember: {
        id: targetMember.id,
        user: targetMember.user,
        role: targetMember.role,
        joinedAt: targetMember.joinedAt
      },
      removedBy: {
        id: user.id,
        username: user.username,
        role: currentUserMember.role
      }
    })
  } catch (error) {
    console.error('Remove member error:', error)
    return NextResponse.json(
      { error: 'Failed to remove member' },
      { status: 500 }
    )
  }
}

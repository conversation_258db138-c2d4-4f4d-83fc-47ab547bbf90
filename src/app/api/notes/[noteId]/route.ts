import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const updateNoteSchema = z.object({
  title: z.string().min(1, 'Note title is required').max(200, 'Title too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
})

// GET /api/notes/[noteId] - Get specific note with blocks
export async function GET(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { noteId } = await params

    // Get note with blocks and check access
    const note = await prisma.note.findFirst({
      where: {
        id: noteId,
        group: {
          members: {
            some: { userId: user.id }
          }
        }
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        group: {
          select: {
            id: true,
            name: true,
          }
        },
        blocks: {
          include: {
            author: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        }
      }
    })

    if (!note) {
      return NextResponse.json(
        { error: 'Note not found or access denied' },
        { status: 404 }
      )
    }

    return NextResponse.json({ note })
  } catch (error) {
    console.error('Get note error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch note' },
      { status: 500 }
    )
  }
}

// PUT /api/notes/[noteId] - Update note
export async function PUT(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { noteId } = await params
    const body = await request.json()
    const updateData = updateNoteSchema.parse(body)

    // Check if user has access to the note
    const note = await prisma.note.findFirst({
      where: {
        id: noteId,
        group: {
          members: {
            some: { userId: user.id }
          }
        }
      }
    })

    if (!note) {
      return NextResponse.json(
        { error: 'Note not found or access denied' },
        { status: 404 }
      )
    }

    // Update note
    const updatedNote = await prisma.note.update({
      where: { id: noteId },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        blocks: {
          include: {
            author: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Note updated successfully',
      note: updatedNote
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Update note error:', error)
    return NextResponse.json(
      { error: 'Failed to update note' },
      { status: 500 }
    )
  }
}

// DELETE /api/notes/[noteId] - Delete note
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { noteId } = await params

    // Check if user is the author or has admin/owner role in the group
    const note = await prisma.note.findFirst({
      where: {
        id: noteId,
        OR: [
          { authorId: user.id },
          {
            group: {
              OR: [
                { ownerId: user.id },
                {
                  members: {
                    some: {
                      userId: user.id,
                      role: { in: ['OWNER', 'ADMIN'] }
                    }
                  }
                }
              ]
            }
          }
        ]
      }
    })

    if (!note) {
      return NextResponse.json(
        { error: 'Note not found or insufficient permissions' },
        { status: 404 }
      )
    }

    await prisma.note.delete({
      where: { id: noteId }
    })

    return NextResponse.json({
      message: 'Note deleted successfully'
    })
  } catch (error) {
    console.error('Delete note error:', error)
    return NextResponse.json(
      { error: 'Failed to delete note' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const createBlockSchema = z.object({
  type: z.enum(['TEXT', 'HEADING_1', 'HEADING_2', 'HEADING_3', 'BULLET_LIST', 'NUMBERED_LIST', 'CODE', 'QUOTE']),
  content: z.string().max(5000, 'Content too long'),
  order: z.number().int().min(0),
})

const updateBlockSchema = z.object({
  content: z.string().max(5000, 'Content too long').optional(),
  type: z.enum(['TEXT', 'HEADING_1', 'HEADING_2', 'HEADING_3', 'BULLET_LIST', 'NUMBERED_LIST', 'CODE', 'QUOTE']).optional(),
  order: z.number().int().min(0).optional(),
})

// POST /api/notes/[noteId]/blocks - Create a new block
export async function POST(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { noteId } = await params
    const body = await request.json()
    const { type, content, order } = createBlockSchema.parse(body)

    // Check if user has access to the note
    const note = await prisma.note.findFirst({
      where: {
        id: noteId,
        group: {
          members: {
            some: { userId: user.id }
          }
        }
      }
    })

    if (!note) {
      return NextResponse.json(
        { error: 'Note not found or access denied' },
        { status: 404 }
      )
    }

    // Adjust order of existing blocks if necessary
    await prisma.noteBlock.updateMany({
      where: {
        noteId: noteId,
        order: { gte: order }
      },
      data: {
        order: { increment: 1 }
      }
    })

    // Create new block
    const block = await prisma.noteBlock.create({
      data: {
        type,
        content,
        order,
        noteId,
        authorId: user.id
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Block created successfully',
      block
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Create block error:', error)
    return NextResponse.json(
      { error: 'Failed to create block' },
      { status: 500 }
    )
  }
}

// PUT /api/notes/[noteId]/blocks - Update multiple blocks (for reordering)
export async function PUT(request: NextRequest, { params }: { params: Promise<{ noteId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { noteId } = await params
    const body = await request.json()
    
    const updateBlocksSchema = z.object({
      blocks: z.array(z.object({
        id: z.string(),
        order: z.number().int().min(0)
      }))
    })

    const { blocks } = updateBlocksSchema.parse(body)

    // Check if user has access to the note
    const note = await prisma.note.findFirst({
      where: {
        id: noteId,
        group: {
          members: {
            some: { userId: user.id }
          }
        }
      }
    })

    if (!note) {
      return NextResponse.json(
        { error: 'Note not found or access denied' },
        { status: 404 }
      )
    }

    // Update blocks in a transaction
    await prisma.$transaction(
      blocks.map(block => 
        prisma.noteBlock.update({
          where: { id: block.id },
          data: { order: block.order }
        })
      )
    )

    // Get updated blocks
    const updatedBlocks = await prisma.noteBlock.findMany({
      where: { noteId },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        }
      },
      orderBy: { order: 'asc' }
    })

    return NextResponse.json({
      message: 'Blocks updated successfully',
      blocks: updatedBlocks
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Update blocks error:', error)
    return NextResponse.json(
      { error: 'Failed to update blocks' },
      { status: 500 }
    )
  }
}

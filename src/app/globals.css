@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Code block styling improvements */
.code-block {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  line-height: 1.5;
  tab-size: 2;
}

/* Improved contrast for accessibility */
.high-contrast-input {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

.high-contrast-input::placeholder {
  color: #6b7280 !important;
  opacity: 1;
}

/* Dark theme code blocks */
.code-dark {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border: 1px solid #334155;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Syntax highlighting colors */
.syntax-keyword { color: #f59e0b; }
.syntax-string { color: #10b981; }
.syntax-comment { color: #6b7280; font-style: italic; }
.syntax-number { color: #3b82f6; }

/* Mobile thread optimizations */
.thread-container {
  scroll-behavior: smooth;
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }
}

/* Improved line clamping for mobile */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Better scroll indicators for mobile */
.scroll-indicator {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.scroll-indicator::-webkit-scrollbar {
  width: 4px;
}

.scroll-indicator::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-indicator::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 2px;
}

/* Mobile layout optimizations */
@media (max-width: 768px) {
  .thread-reply {
    margin-left: 0.5rem;
  }

  .reply-button {
    width: 3rem;
    height: 3rem;
  }

  .mobile-thread-nav {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 0.5rem;
    margin: 0.5rem 0;
  }

  .mobile-thread-nav button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile sidebar animations */
  .mobile-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .mobile-sidebar.open {
    transform: translateX(0);
  }

  /* Touch-friendly message layout */
  .message-container {
    padding: 0.5rem;
  }

  .message-bubble {
    max-width: 85%;
  }

  /* Improved touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile input optimizations */
  .mobile-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile */
  .mobile-spacing {
    padding: 0.75rem;
  }

  /* Optimized button sizes for mobile */
  .mobile-button {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem;
  }
}

/* Thread display improvements */
.thread-reply {
  position: relative;
}

.thread-reply::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #3b82f6, #60a5fa);
  opacity: 0.6;
}

.thread-depth-1::before {
  background: linear-gradient(to bottom, #3b82f6, #60a5fa);
}

.thread-depth-2::before {
  background: linear-gradient(to bottom, #8b5cf6, #a78bfa);
}

.thread-depth-3::before {
  background: linear-gradient(to bottom, #10b981, #34d399);
}

/* Enhanced message bubbles for threads */
.thread-message {
  transition: all 0.2s ease-in-out;
}

.thread-message:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Notes block menu improvements */
.block-menu {
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

.block-menu-item {
  transition: all 0.15s ease-in-out;
}

.block-menu-item:hover {
  background: rgba(59, 130, 246, 0.05);
  border-left: 3px solid #3b82f6;
  padding-left: calc(1rem - 3px);
}

/* Improved timestamp display */
.timestamp {
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

/* Better visual hierarchy for thread indicators */
.thread-indicator {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.thread-indicator svg {
  margin-right: 0.25rem;
  opacity: 0.7;
}

'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { GroupProvider } from '@/contexts/GroupContext'
import { useMobileNavigation } from '@/hooks/useMobileNavigation'
import GroupList from '@/components/groups/GroupList'
import GroupDetail from '@/components/groups/GroupDetail'

export default function DashboardPage() {
  const { user, loading, logout } = useAuth()
  const router = useRouter()
  const { isSidebarOpen, isMobile, toggleSidebar, closeSidebar } = useMobileNavigation()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to auth
  }

  const handleLogout = async () => {
    await logout()
    router.push('/auth')
  }

  return (
    <GroupProvider>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        {/* Header */}
        <header className="bg-white shadow relative z-30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center">
                {/* Mobile hamburger menu */}
                {isMobile && (
                  <button
                    onClick={toggleSidebar}
                    className="mr-3 p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="Toggle sidebar"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </button>
                )}
                <h1 className="text-xl md:text-2xl font-bold text-gray-900">MyBinder</h1>
              </div>
              <div className="flex items-center space-x-2 md:space-x-4">
                <div className="text-xs md:text-sm text-gray-700 hidden sm:block">
                  Welcome, <span className="font-medium">{user.name || user.username}</span>
                </div>
                <button
                  onClick={handleLogout}
                  className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 md:px-4 rounded text-xs md:text-sm"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 flex relative">
          {/* Mobile backdrop */}
          {isMobile && isSidebarOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-20"
              onClick={closeSidebar}
            />
          )}

          <GroupList
            isMobile={isMobile}
            isSidebarOpen={isSidebarOpen}
            closeSidebar={closeSidebar}
          />
          <GroupDetail
            isMobile={isMobile}
            isSidebarOpen={isSidebarOpen}
            toggleSidebar={toggleSidebar}
          />
        </div>
      </div>
    </GroupProvider>
  )
}

'use client'

import { useState } from 'react'
import { useGroups } from '@/contexts/GroupContext'
import { useAuth } from '@/contexts/AuthContext'
import CreateGroupModal from './CreateGroupModal'

interface GroupListProps {
  isMobile: boolean
  isSidebarOpen: boolean
  closeSidebar: () => void
}

export default function GroupList({ isMobile, isSidebarOpen, closeSidebar }: GroupListProps) {
  const { groups, selectedGroup, selectGroup, loading } = useGroups()
  const { user } = useAuth()
  const [showCreateModal, setShowCreateModal] = useState(false)

  const handleGroupSelect = (group: any) => {
    selectGroup(group)
    // Auto-close sidebar on mobile after group selection
    if (isMobile) {
      closeSidebar()
    }
  }

  if (loading) {
    return (
      <div className={`
        ${isMobile
          ? `fixed inset-y-0 left-0 z-30 w-80 transform transition-transform duration-300 ease-in-out ${
              isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
            }`
          : 'w-80'
        }
        bg-white border-r border-gray-200 p-4
      `}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className={`
        ${isMobile
          ? `fixed inset-y-0 left-0 z-30 w-80 transform transition-transform duration-300 ease-in-out ${
              isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
            }`
          : 'w-80'
        }
        bg-white border-r border-gray-200 flex flex-col
      `}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Groups</h2>
            <div className="flex items-center space-x-2">
              {/* Close button for mobile */}
              {isMobile && (
                <button
                  onClick={closeSidebar}
                  className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  aria-label="Close sidebar"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full"
                title="Create new group"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Groups List */}
        <div className="flex-1 overflow-y-auto">
          {groups.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <p className="mb-2">No groups yet</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="text-blue-500 hover:text-blue-600 text-sm"
              >
                Create your first group
              </button>
            </div>
          ) : (
            <div className="p-2">
              {groups.map((group) => (
                <div
                  key={group.id}
                  onClick={() => handleGroupSelect(group)}
                  className={`p-3 rounded-lg cursor-pointer mb-2 transition-colors ${
                    selectedGroup?.id === group.id
                      ? 'bg-blue-50 border border-blue-200'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {group.name}
                        </h3>
                        {group.isPrivate && (
                          <svg className="w-3 h-3 text-gray-400 ml-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      {group.description && (
                        <p className="text-xs text-gray-500 truncate mt-1">
                          {group.description}
                        </p>
                      )}
                      <div className="flex items-center mt-2 text-xs text-gray-400">
                        <span>{group._count.members} members</span>
                        <span className="mx-1">•</span>
                        <span>{group._count.messages} messages</span>
                        <span className="mx-1">•</span>
                        <span>{group._count.notes} notes</span>
                      </div>
                    </div>
                    {group.ownerId === user?.id && (
                      <div className="ml-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Owner
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <CreateGroupModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </>
  )
}

'use client'

import { useState } from 'react'
import { useGroups } from '@/contexts/GroupContext'
import { useAuth } from '@/contexts/AuthContext'
import { GroupWithMembers } from '@/types'

interface TransferOwnershipModalProps {
  isOpen: boolean
  onClose: () => void
  group: GroupWithMembers
}

export default function TransferOwnershipModal({ isOpen, onClose, group }: TransferOwnershipModalProps) {
  const { transferOwnership } = useGroups()
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [selectedMemberId, setSelectedMemberId] = useState('')

  if (!isOpen) return null

  // Get eligible members (exclude current owner)
  const eligibleMembers = group.members.filter(member => member.userId !== user?.id)

  const handleTransfer = async () => {
    if (!selectedMemberId) {
      setError('Please select a member to transfer ownership to')
      return
    }

    setError('')
    setLoading(true)

    try {
      await transferOwnership(group.id, selectedMemberId)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to transfer ownership')
    } finally {
      setLoading(false)
    }
  }

  const selectedMember = eligibleMembers.find(m => m.userId === selectedMemberId)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Transfer Ownership</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="mb-6">
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            <div className="flex">
              <svg className="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="font-medium">Important: This action cannot be undone</p>
                <p className="text-sm">
                  You will lose all owner privileges and become a regular member. 
                  The new owner will have full control over the group.
                </p>
              </div>
            </div>
          </div>

          <p className="text-gray-700 mb-4">
            Select a member to transfer ownership of <strong>{group.name}</strong> to:
          </p>

          {eligibleMembers.length === 0 ? (
            <div className="bg-gray-100 p-4 rounded">
              <p className="text-gray-600">
                No other members available. You need at least one other member to transfer ownership.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {eligibleMembers.map((member) => (
                <label
                  key={member.id}
                  className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedMemberId === member.userId
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="newOwner"
                    value={member.userId}
                    checked={selectedMemberId === member.userId}
                    onChange={(e) => setSelectedMemberId(e.target.value)}
                    className="mr-3"
                  />
                  <div className="flex items-center flex-1">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                      <span className="text-sm font-medium text-gray-700">
                        {(member.user.name || member.user.username).charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {member.user.name || member.user.username}
                      </p>
                      <p className="text-xs text-gray-500">{member.user.email}</p>
                    </div>
                    <span className={`ml-auto inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      member.role === 'ADMIN' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {member.role}
                    </span>
                  </div>
                </label>
              ))}
            </div>
          )}

          {selectedMember && (
            <div className="mt-4 bg-blue-100 p-3 rounded">
              <p className="text-sm text-blue-700">
                <strong>{selectedMember.user.name || selectedMember.user.username}</strong> will become the new owner and you will become a regular member.
              </p>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleTransfer}
            disabled={loading || !selectedMemberId || eligibleMembers.length === 0}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Transferring...' : 'Transfer Ownership'}
          </button>
        </div>
      </div>
    </div>
  )
}

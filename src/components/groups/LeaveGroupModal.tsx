'use client'

import { useState } from 'react'
import { useGroups } from '@/contexts/GroupContext'
import { useAuth } from '@/contexts/AuthContext'
import { GroupWithMembers } from '@/types'

interface LeaveGroupModalProps {
  isOpen: boolean
  onClose: () => void
  group: GroupWithMembers
}

export default function LeaveGroupModal({ isOpen, onClose, group }: LeaveGroupModalProps) {
  const { leaveGroup } = useGroups()
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  if (!isOpen) return null

  // Check if user is owner
  const isOwner = group.ownerId === user?.id
  const memberCount = group._count.members
  const hasOtherMembers = memberCount > 1

  const handleLeave = async () => {
    setError('')
    setLoading(true)

    try {
      await leaveGroup(group.id)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to leave group')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Leave Group</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="mb-6">
          <p className="text-gray-700 mb-4">
            Are you sure you want to leave <strong>{group.name}</strong>?
          </p>

          {isOwner && hasOtherMembers && (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
              <div className="flex">
                <svg className="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="font-medium">Warning: You are the group owner</p>
                  <p className="text-sm">
                    As the owner, you cannot leave while there are other members. 
                    Please transfer ownership to another member first or delete the group.
                  </p>
                </div>
              </div>
            </div>
          )}

          {!isOwner && (
            <div className="bg-gray-100 p-3 rounded">
              <p className="text-sm text-gray-600">
                You will no longer have access to messages, notes, and other group content.
                You can rejoin if this is a public group or if you receive another invitation.
              </p>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleLeave}
            disabled={loading || (isOwner && hasOtherMembers)}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Leaving...' : 'Leave Group'}
          </button>
        </div>
      </div>
    </div>
  )
}

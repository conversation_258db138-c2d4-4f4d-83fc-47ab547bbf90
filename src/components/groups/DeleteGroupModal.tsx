'use client'

import { useState } from 'react'
import { useGroups } from '@/contexts/GroupContext'
import { GroupWithMembers } from '@/types'

interface DeleteGroupModalProps {
  isOpen: boolean
  onClose: () => void
  group: GroupWithMembers
}

export default function DeleteGroupModal({ isOpen, onClose, group }: DeleteGroupModalProps) {
  const { deleteGroup } = useGroups()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [confirmText, setConfirmText] = useState('')

  if (!isOpen) return null

  const isConfirmValid = confirmText === group.name

  const handleDelete = async () => {
    if (!isConfirmValid) return

    setError('')
    setLoading(true)

    try {
      await deleteGroup(group.id)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete group')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-red-600">Delete Group</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="mb-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <div className="flex">
              <svg className="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="font-medium">This action cannot be undone!</p>
                <p className="text-sm">
                  This will permanently delete the group and all associated data including:
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-100 p-4 rounded mb-4">
            <h3 className="font-medium text-gray-900 mb-2">What will be deleted:</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• {group._count.members} member{group._count.members !== 1 ? 's' : ''}</li>
              <li>• {group._count.messages} message{group._count.messages !== 1 ? 's' : ''}</li>
              <li>• {group._count.notes} note{group._count.notes !== 1 ? 's' : ''}</li>
              <li>• All group settings and permissions</li>
            </ul>
          </div>

          <p className="text-gray-700 mb-4">
            To confirm deletion, please type the group name <strong>{group.name}</strong> below:
          </p>

          <input
            type="text"
            value={confirmText}
            onChange={(e) => setConfirmText(e.target.value)}
            placeholder={`Type "${group.name}" to confirm`}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
          />
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            disabled={loading || !isConfirmValid}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Deleting...' : 'Delete Group'}
          </button>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect, useRef } from 'react'
import { useNotes } from '@/contexts/NotesContext'
import { useSmartPosition } from '@/hooks/useSmartPosition'
import { NoteWithBlocks } from '@/types'
import BlockEditor from './BlockEditor'

interface NoteEditorProps {
  note: NoteWithBlocks
}

function NoteEditor({ note: initialNote }: NoteEditorProps) {
  const { selectedNote, updateNote, createBlock, loadNote, loadingNote } = useNotes()
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [title, setTitle] = useState(initialNote.title)
  const [description, setDescription] = useState(initialNote.description || '')
  const [showBlockTypeMenu, setShowBlockTypeMenu] = useState(false)
  const blockTypeButtonRef = useRef<HTMLButtonElement>(null)

  // Smart positioning for block type menu
  const { getPositionStyles } = useSmartPosition(
    blockTypeButtonRef,
    showBlockTypeMenu,
    {
      preferredPosition: 'bottom',
      offset: 8,
      menuHeight: 400, // Estimated height of the block type menu
      menuWidth: 224, // w-56 = 14rem = 224px
    }
  )

  const note = selectedNote || initialNote

  useEffect(() => {
    if (initialNote.id) {
      loadNote(initialNote.id)
    }
  }, [initialNote.id])

  const handleSaveTitle = async () => {
    try {
      await updateNote(note.id, { title, description: description || undefined })
      setIsEditingTitle(false)
    } catch (error) {
      console.error('Failed to update note:', error)
      alert('Failed to update note')
    }
  }

  const handleAddBlock = async (type: string = 'TEXT') => {
    try {
      const newOrder = note.blocks.length
      await createBlock(note.id, type, '', newOrder)
      setShowBlockTypeMenu(false) // Close menu after adding block
    } catch (error) {
      console.error('Failed to create block:', error)
      alert('Failed to create block')
    }
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Note Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        {isEditingTitle ? (
          <div className="space-y-4">
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="text-2xl font-bold text-gray-900 w-full border-none outline-none bg-transparent"
              placeholder="Note title"
              autoFocus
            />
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="text-gray-600 w-full border-none outline-none bg-transparent resize-none"
              placeholder="Add a description..."
              rows={2}
            />
            <div className="flex space-x-2">
              <button
                onClick={handleSaveTitle}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
              >
                Save
              </button>
              <button
                onClick={() => {
                  setTitle(note.title)
                  setDescription(note.description || '')
                  setIsEditingTitle(false)
                }}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div onClick={() => setIsEditingTitle(true)} className="cursor-pointer">
            <h1 className="text-2xl font-bold text-gray-900 mb-2 hover:bg-gray-50 p-2 rounded">
              {note.title}
            </h1>
            {note.description && (
              <p className="text-gray-600 hover:bg-gray-50 p-2 rounded">
                {note.description}
              </p>
            )}
          </div>
        )}
        
        <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
          <div>
            Created by <span className="font-medium">{note.author.name || note.author.username}</span>
          </div>
          <div>
            Last updated {formatDate(note.updatedAt)}
          </div>
        </div>
      </div>

      {/* Note Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto p-6">
          {/* Loading State */}
          {(loadingNote || !note.blocks) && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-500">Loading note content...</span>
            </div>
          )}

          {/* Blocks */}
          {note.blocks && (
            <div className="space-y-2">
              {note.blocks.map((block, index) => (
                <BlockEditor
                  key={block.id}
                  block={block}
                  noteId={note.id}
                  isLast={index === note.blocks.length - 1}
                  onAddBlock={handleAddBlock}
                />
              ))}
            </div>
          )}

          {/* Add Block Button */}
          {note.blocks && (
            <div className="mt-6">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleAddBlock('TEXT')}
                  className="flex items-center space-x-2 text-gray-500 hover:text-gray-700 p-3 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span className="text-sm font-medium">Add block</span>
                </button>

                {/* Block Type Selector with smart positioning */}
                <div className="relative">
                  <button
                    ref={blockTypeButtonRef}
                    onClick={() => setShowBlockTypeMenu(!showBlockTypeMenu)}
                    className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                    title="Choose block type"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {showBlockTypeMenu && (
                    <>
                      {/* Backdrop to close menu */}
                      <div
                        className="fixed inset-0 z-10"
                        onClick={() => setShowBlockTypeMenu(false)}
                      />

                      {/* Menu with smart positioning */}
                      <div
                        className="w-56 bg-white border border-gray-200 rounded-lg shadow-lg"
                        style={getPositionStyles()}
                      >
                        <div className="py-2">
                          <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                            Block Types
                          </div>
                          <button
                            onClick={() => handleAddBlock('TEXT')}
                            className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          >
                            <span className="mr-3 text-lg">📝</span>
                            <div>
                              <div className="font-medium">Text</div>
                              <div className="text-xs text-gray-500">Plain text paragraph</div>
                            </div>
                          </button>
                          <button
                            onClick={() => handleAddBlock('HEADING')}
                            className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          >
                            <span className="mr-3 text-lg">📰</span>
                            <div>
                              <div className="font-medium">Heading</div>
                              <div className="text-xs text-gray-500">Large section title</div>
                            </div>
                          </button>
                          <button
                            onClick={() => handleAddBlock('BULLET_LIST')}
                            className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          >
                            <span className="mr-3 text-lg">•</span>
                            <div>
                              <div className="font-medium">Bullet List</div>
                              <div className="text-xs text-gray-500">Unordered list items</div>
                            </div>
                          </button>
                          <button
                            onClick={() => handleAddBlock('NUMBERED_LIST')}
                            className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          >
                            <span className="mr-3 text-lg">1.</span>
                            <div>
                              <div className="font-medium">Numbered List</div>
                              <div className="text-xs text-gray-500">Ordered list items</div>
                            </div>
                          </button>
                          <button
                            onClick={() => handleAddBlock('CODE')}
                            className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          >
                            <span className="mr-3 text-lg">💻</span>
                            <div>
                              <div className="font-medium">Code</div>
                              <div className="text-xs text-gray-500">Code snippet with syntax highlighting</div>
                            </div>
                          </button>
                          <button
                            onClick={() => handleAddBlock('QUOTE')}
                            className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          >
                            <span className="mr-3 text-lg">💬</span>
                            <div>
                              <div className="font-medium">Quote</div>
                              <div className="text-xs text-gray-500">Highlighted quotation</div>
                            </div>
                          </button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default NoteEditor

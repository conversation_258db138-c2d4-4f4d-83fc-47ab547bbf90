'use client'

import { useState, useEffect } from 'react'
import { useNotes } from '@/contexts/NotesContext'
import { GroupWithMembers } from '@/types'
import CreateNoteModal from './CreateNoteModal'
import NoteEditor from './NoteEditor'

interface NotesInterfaceProps {
  group: GroupWithMembers
}

export default function NotesInterface({ group }: NotesInterfaceProps) {
  const { notes, selectedNote, loading, loadNotes, selectNote, deleteNote } = useNotes()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showNotesList, setShowNotesList] = useState(true)

  useEffect(() => {
    if (group) {
      loadNotes(group.id)
    }
  }, [group.id])

  const handleDeleteNote = async (noteId: string) => {
    if (confirm('Are you sure you want to delete this note?')) {
      try {
        await deleteNote(noteId)
      } catch (error) {
        console.error('Failed to delete note:', error)
        alert('Failed to delete note')
      }
    }
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (selectedNote && !showNotesList) {
    return (
      <div className="flex-1 flex flex-col">
        <div className="bg-white border-b border-gray-200 p-4">
          <button
            onClick={() => setShowNotesList(true)}
            className="text-blue-500 hover:text-blue-600 text-sm mb-2"
          >
            ← Back to Notes
          </button>
        </div>
        <NoteEditor note={selectedNote} />
      </div>
    )
  }

  return (
    <>
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold text-gray-900">Notes</h2>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
            >
              New Note
            </button>
          </div>
        </div>

        {/* Notes List */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-gray-500">Loading notes...</p>
            </div>
          ) : notes.length === 0 ? (
            <div className="text-center py-8">
              <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Notes Yet</h3>
              <p className="text-gray-500 mb-4">Create your first note to start collaborating</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
              >
                Create Note
              </button>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {notes.map((note) => (
                <div
                  key={note.id}
                  className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer border border-gray-200"
                  onClick={() => {
                    selectNote(note)
                    setShowNotesList(false)
                  }}
                >
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {note.title}
                      </h3>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteNote(note.id)
                        }}
                        className="text-gray-400 hover:text-red-500 p-1"
                        title="Delete note"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                    
                    {note.description && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {note.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center">
                        <span className="font-medium">
                          {note.author.name || note.author.username}
                        </span>
                        <span className="mx-1">•</span>
                        <span>{note._count?.blocks || note.blocks?.length || 0} blocks</span>
                      </div>
                      <span>{formatDate(note.updatedAt)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <CreateNoteModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        groupId={group.id}
      />
    </>
  )
}

'use client'

import { useState, useEffect, useRef } from 'react'
import { useMessages } from '@/contexts/MessageContext'
import { useAuth } from '@/contexts/AuthContext'
import { GroupWithMembers, MessageWithAuthor } from '@/types'
import ReplyButton from '@/components/threads/ReplyButton'
import ThreadReply from '@/components/threads/ThreadReply'
import ReplyInput from '@/components/threads/ReplyInput'
import ThreadIndicator from '@/components/threads/ThreadIndicator'
import MobileThreadNavigation from '@/components/threads/MobileThreadNavigation'
import { useIsMobile, useIsTouch } from '@/hooks/useIsMobile'
import { useSwipeGesture } from '@/hooks/useSwipeGesture'

interface ChatInterfaceProps {
  group: GroupWithMembers
}

export default function ChatInterface({ group }: ChatInterfaceProps) {
  const { messages, loading, sending, sendMessage, loadMessages, hasMore, currentPage } = useMessages()
  const { user } = useAuth()
  const [newMessage, setNewMessage] = useState('')
  const [error, setError] = useState('')
  const [replyingTo, setReplyingTo] = useState<MessageWithAuthor | null>(null)
  const [expandedThreads, setExpandedThreads] = useState<Set<string>>(new Set())
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // Mobile detection
  const isMobile = useIsMobile()
  const isTouch = useIsTouch()

  useEffect(() => {
    if (group) {
      loadMessages(group.id)
    }
  }, [group.id])

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || sending) return

    setError('')
    const messageContent = newMessage.trim()
    setNewMessage('')

    try {
      await sendMessage(group.id, messageContent)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message')
      setNewMessage(messageContent) // Restore message on error
    }
  }

  const loadMoreMessages = async () => {
    if (hasMore && !loading) {
      await loadMessages(group.id, currentPage + 1)
    }
  }

  const handleReply = (messageId: string) => {
    const message = messages.find(m => m.id === messageId)
    if (message) {
      setReplyingTo(message)
    }
  }

  const handleCancelReply = () => {
    setReplyingTo(null)
  }

  const handleReplySuccess = () => {
    setReplyingTo(null)
    scrollToBottom()
  }

  const toggleThread = (messageId: string) => {
    setExpandedThreads(prev => {
      const newSet = new Set(prev)
      if (newSet.has(messageId)) {
        newSet.delete(messageId)
      } else {
        newSet.add(messageId)
      }
      return newSet
    })
  }

  // Swipe gesture for mobile thread navigation
  const swipeGesture = useSwipeGesture({
    onSwipeLeft: () => {
      // Close reply input on swipe left
      if (replyingTo) {
        setReplyingTo(null)
      }
    },
    onSwipeRight: () => {
      // Could be used for other navigation
    },
    threshold: 100
  })

  const formatTime = (date: string | Date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const formatDetailedTime = (date: string | Date) => {
    const messageDate = new Date(date)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    // Show relative time for recent messages
    if (diffInMinutes < 1) {
      return 'Just now'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else if (diffInDays < 7) {
      return `${diffInDays}d ago`
    } else {
      // Show full date for older messages
      return messageDate.toLocaleDateString([], {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  const formatDate = (date: string | Date) => {
    const messageDate = new Date(date)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return messageDate.toLocaleDateString([], {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
  }

  const shouldShowDateSeparator = (currentMessage: any, previousMessage: any) => {
    if (!previousMessage) return true
    
    const currentDate = new Date(currentMessage.createdAt).toDateString()
    const previousDate = new Date(previousMessage.createdAt).toDateString()
    
    return currentDate !== previousDate
  }

  return (
    <div className="flex flex-col h-full">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-2 md:p-4 space-y-3 md:space-y-4 thread-container scroll-indicator"
        data-testid="messages-container"
        {...(isTouch ? swipeGesture : {})}
      >
        {/* Load More Button */}
        {hasMore && (
          <div className="text-center">
            <button
              onClick={loadMoreMessages}
              disabled={loading}
              className="text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Load older messages'}
            </button>
          </div>
        )}

        {/* Messages */}
        {messages.map((message, index) => {
          const previousMessage = index > 0 ? messages[index - 1] : null
          const showDateSeparator = shouldShowDateSeparator(message, previousMessage)
          const isOwnMessage = message.authorId === user?.id

          return (
            <div key={message.id} data-testid="message">
              {/* Date Separator */}
              {showDateSeparator && (
                <div className="flex items-center justify-center my-4">
                  <div className="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full">
                    {formatDate(message.createdAt)}
                  </div>
                </div>
              )}

              {/* Message */}
              <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[85%] sm:max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                  {!isOwnMessage && (
                    <div className="text-xs text-gray-700 mb-1 font-medium">
                      {message.author.name || message.author.username}
                    </div>
                  )}

                  <div className="group relative">
                    <div
                      className={`px-4 py-2 rounded-lg ${
                        isOwnMessage
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-900 border border-gray-200'
                      }`}
                    >
                      <div className="text-sm whitespace-pre-wrap">
                        {message.content.includes('```') ? (
                          <div>
                            {message.content.split('```').map((part, index) =>
                              index % 2 === 0 ? (
                                <span key={index}>{part}</span>
                              ) : (
                                <code key={index} className="block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto">
                                  {part}
                                </code>
                              )
                            )}
                          </div>
                        ) : (
                          message.content
                        )}
                      </div>

                      <div className="flex items-center justify-between mt-1">
                        <div className={`text-xs ${isOwnMessage ? 'text-blue-100' : 'text-gray-600'}`}>
                          <span className="timestamp" title={new Date(message.createdAt).toLocaleString()}>
                            {formatDetailedTime(message.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Reply button - shows on hover for desktop, always visible on mobile */}
                    <div className={`
                      absolute top-1/2 transform -translate-y-1/2
                      ${isOwnMessage ? '-left-12 sm:-left-14' : '-right-12 sm:-right-14'}
                      ${isMobile ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
                      transition-opacity duration-200
                      z-10
                    `}>
                      <ReplyButton
                        messageId={message.id}
                        onReply={handleReply}
                        isMobile={isMobile}
                      />
                    </div>
                  </div>

                  {/* Thread indicator - desktop only */}
                  {message._count?.replies && message._count.replies > 0 && (
                    <div className={`mt-2 hidden md:flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                      <ThreadIndicator
                        replyCount={message._count.replies}
                        isExpanded={expandedThreads.has(message.id)}
                        onClick={() => toggleThread(message.id)}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Mobile thread navigation */}
              {isMobile && (
                <MobileThreadNavigation
                  message={message}
                  isExpanded={expandedThreads.has(message.id)}
                  onToggle={() => toggleThread(message.id)}
                  onReply={() => handleReply(message.id)}
                  replyCount={message._count?.replies || 0}
                />
              )}

              {/* Thread replies */}
              {message.replies && message.replies.length > 0 && expandedThreads.has(message.id) && (
                <div className="mt-2 ml-4">
                  {message.replies.map((reply) => (
                    <ThreadReply
                      key={reply.id}
                      message={reply}
                      depth={1}
                      onReply={handleReply}
                      maxDepth={3}
                    />
                  ))}
                </div>
              )}

              {/* Reply input */}
              {replyingTo?.id === message.id && (
                <div className="mt-2">
                  <ReplyInput
                    groupId={group.id}
                    parentMessage={replyingTo}
                    onCancel={handleCancelReply}
                    onSuccess={handleReplySuccess}
                  />
                </div>
              )}
            </div>
          )
        })}

        {/* Loading indicator */}
        {loading && messages.length === 0 && (
          <div className="text-center text-gray-500">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-2">Loading messages...</p>
          </div>
        )}

        {/* Empty state */}
        {!loading && messages.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet. Start the conversation!</p>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t border-gray-200 p-3 md:p-4">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            className="flex-1 border border-gray-300 rounded-lg px-3 md:px-4 py-2 md:py-2 high-contrast-input focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500 text-sm md:text-base"
            disabled={sending}
            maxLength={2000}
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || sending}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 md:px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed text-sm md:text-base min-h-[44px] min-w-[44px] flex items-center justify-center"
          >
            <span className="hidden sm:inline">{sending ? 'Sending...' : 'Send'}</span>
            <span className="sm:hidden">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </span>
          </button>
        </form>
      </div>
    </div>
  )
}

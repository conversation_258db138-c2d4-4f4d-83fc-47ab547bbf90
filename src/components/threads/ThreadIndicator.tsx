'use client'

interface ThreadIndicatorProps {
  replyCount: number
  isExpanded: boolean
  onClick: () => void
  className?: string
}

export default function ThreadIndicator({ 
  replyCount, 
  isExpanded, 
  onClick, 
  className = '' 
}: ThreadIndicatorProps) {
  if (replyCount === 0) return null

  return (
    <button
      onClick={onClick}
      className={`
        inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs
        bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700
        border border-blue-200 hover:border-blue-300
        transition-all duration-200
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
        ${className}
      `}
      aria-label={`${isExpanded ? 'Hide' : 'Show'} ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`}
    >
      <svg 
        width="12" 
        height="12" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
        className={`transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
      >
        <path d="M9 18l6-6-6-6"/>
      </svg>
      
      <span className="font-medium">
        {replyCount} {replyCount === 1 ? 'reply' : 'replies'}
      </span>
    </button>
  )
}

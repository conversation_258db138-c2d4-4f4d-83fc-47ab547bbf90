'use client'

import { useState } from 'react'
import { hapticFeedback } from '@/lib/haptics'

interface ReplyButtonProps {
  messageId: string
  onReply: (messageId: string) => void
  className?: string
  isMobile?: boolean
}

export default function ReplyButton({ messageId, onReply, className = '', isMobile = false }: ReplyButtonProps) {
  const [isPressed, setIsPressed] = useState(false)

  const handleClick = () => {
    hapticFeedback.light()
    onReply(messageId)
  }

  const handleTouchStart = () => {
    setIsPressed(true)
    hapticFeedback.light()
  }

  const handleTouchEnd = () => {
    setIsPressed(false)
  }

  return (
    <button
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      data-testid="reply-button"
      className={`
        inline-flex items-center justify-center
        ${isMobile ? 'w-12 h-12 md:w-10 md:h-10' : 'w-8 h-8'}
        rounded-full
        bg-white hover:bg-gray-50
        text-gray-600 hover:text-gray-800
        border border-gray-200 hover:border-gray-300
        shadow-sm hover:shadow-md
        transition-all duration-200
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
        active:scale-95
        ${isPressed ? 'scale-95 bg-gray-50' : ''}
        ${className}
      `}
      aria-label="Reply to message"
      title="Reply to message"
    >
      <svg 
        width={isMobile ? "20" : "16"} 
        height={isMobile ? "20" : "16"} 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
      </svg>
    </button>
  )
}

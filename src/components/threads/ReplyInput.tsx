'use client'

import { useState, useRef, useEffect } from 'react'
import { useMessages } from '@/contexts/MessageContext'
import { MessageWithAuthor } from '@/types'

interface ReplyInputProps {
  groupId: string
  parentMessage: MessageWithAuthor
  onCancel: () => void
  onSuccess?: () => void
}

export default function ReplyInput({ 
  groupId, 
  parentMessage, 
  onCancel, 
  onSuccess 
}: ReplyInputProps) {
  const [replyContent, setReplyContent] = useState('')
  const [error, setError] = useState('')
  const { replyToMessage, sending } = useMessages()
  const inputRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    // Auto-focus the input when component mounts
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!replyContent.trim() || sending) return

    setError('')
    const content = replyContent.trim()

    try {
      await replyToMessage(groupId, content, parentMessage.id)
      setReplyContent('')
      onSuccess?.()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send reply')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    } else if (e.key === 'Escape') {
      onCancel()
    }
  }

  return (
    <div className="mt-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
      {/* Parent message preview */}
      <div className="mb-3 p-2 bg-white rounded border-l-4 border-blue-500">
        <div className="text-xs text-gray-600 mb-1">
          Replying to {parentMessage.author.name || parentMessage.author.username}
        </div>
        <div className="text-sm text-gray-800 line-clamp-2">
          {parentMessage.content.length > 100 
            ? `${parentMessage.content.substring(0, 100)}...`
            : parentMessage.content
          }
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-3">
        <textarea
          ref={inputRef}
          value={replyContent}
          onChange={(e) => setReplyContent(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type your reply..."
          className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 placeholder-gray-500"
          rows={3}
          maxLength={2000}
          disabled={sending}
          data-testid="reply-input"
        />
        
        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500">
            {replyContent.length}/2000 characters
          </div>
          
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              disabled={sending}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!replyContent.trim() || sending}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {sending ? 'Sending...' : 'Reply'}
            </button>
          </div>
        </div>
      </form>
      
      <div className="mt-2 text-xs text-gray-500">
        Press Enter to send, Shift+Enter for new line, Esc to cancel
      </div>
    </div>
  )
}

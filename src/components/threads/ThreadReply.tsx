'use client'

import { MessageWithAuthor } from '@/types'
import { useAuth } from '@/contexts/AuthContext'
import ReplyButton from './ReplyButton'

interface ThreadReplyProps {
  message: MessageWithAuthor
  depth: number
  onReply: (messageId: string) => void
  maxDepth?: number
}

export default function ThreadReply({ 
  message, 
  depth, 
  onReply, 
  maxDepth = 3 
}: ThreadReplyProps) {
  const { user } = useAuth()
  const isOwnMessage = message.authorId === user?.id
  const canReply = depth < maxDepth

  const formatDetailedTime = (date: string | Date) => {
    const messageDate = new Date(date)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    // Show relative time for recent messages
    if (diffInMinutes < 1) {
      return 'Just now'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else if (diffInDays < 7) {
      return `${diffInDays}d ago`
    } else {
      // Show full date for older messages
      return messageDate.toLocaleDateString([], {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  // Calculate indentation based on depth - improved visual hierarchy
  const indentationClass = depth > 0 ? `ml-3 md:ml-${Math.min(depth * 6, 16)}` : ''
  const borderClass = depth > 0 ? 'border-l-3 border-blue-300 pl-3 md:pl-4' : ''
  const depthOpacity = depth > 0 ? `opacity-${Math.max(100 - depth * 10, 70)}` : ''

  return (
    <div className={`thread-reply thread-depth-${Math.min(depth, 3)} ${indentationClass} ${borderClass} mt-3 ${depthOpacity}`}>
      {/* Thread depth indicator */}
      {depth > 0 && (
        <div className="thread-indicator">
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
          </svg>
          <span>Reply to {message.parentMessage?.author.name || message.parentMessage?.author.username}</span>
        </div>
      )}

      <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
        <div className={`max-w-[85%] sm:max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
          {!isOwnMessage && (
            <div className="text-xs text-gray-700 mb-1 font-medium flex items-center space-x-2">
              <span>{message.author.name || message.author.username}</span>
              {depth > 0 && (
                <span className="text-gray-400 text-xs">• Thread level {depth}</span>
              )}
            </div>
          )}

          <div className="group relative">
            <div
              className={`thread-message px-3 py-2 rounded-lg text-sm shadow-sm ${
                isOwnMessage
                  ? 'bg-blue-500 text-white'
                  : depth > 0
                    ? 'bg-blue-50 text-gray-900 border border-blue-200'
                    : 'bg-gray-50 text-gray-900 border border-gray-200'
              }`}
            >
              <div className="whitespace-pre-wrap">
                {message.content.includes('```') ? (
                  <div>
                    {message.content.split('```').map((part, index) =>
                      index % 2 === 0 ? (
                        <span key={index}>{part}</span>
                      ) : (
                        <code key={index} className="block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto">
                          {part}
                        </code>
                      )
                    )}
                  </div>
                ) : (
                  message.content
                )}
              </div>

              <div className={`text-xs mt-1 flex items-center justify-between ${isOwnMessage ? 'text-blue-100' : 'text-gray-500'}`}>
                <span className="timestamp" title={new Date(message.createdAt).toLocaleString()}>
                  {formatDetailedTime(message.createdAt)}
                </span>
                {depth > 0 && (
                  <span className={`text-xs ${isOwnMessage ? 'text-blue-200' : 'text-gray-400'}`}>
                    Thread
                  </span>
                )}
              </div>
            </div>

            {/* Reply button - shows on hover for desktop, always visible on mobile */}
            {canReply && (
              <div className={`
                absolute top-1/2 transform -translate-y-1/2
                ${isOwnMessage ? '-left-12 md:-left-10' : '-right-12 md:-right-10'}
                opacity-0 group-hover:opacity-100
                sm:opacity-100
                transition-opacity duration-200
                z-10
              `}>
                <ReplyButton
                  messageId={message.id}
                  onReply={onReply}
                  isMobile={false}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Render nested replies with improved spacing */}
      {message.replies && message.replies.length > 0 && (
        <div className="mt-4 space-y-2">
          <div className="text-xs text-gray-400 mb-2 flex items-center">
            <span>{message.replies.length} {message.replies.length === 1 ? 'reply' : 'replies'}</span>
          </div>
          {message.replies.map((reply) => (
            <ThreadReply
              key={reply.id}
              message={reply}
              depth={depth + 1}
              onReply={onReply}
              maxDepth={maxDepth}
            />
          ))}
        </div>
      )}
    </div>
  )
}

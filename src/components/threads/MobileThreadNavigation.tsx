'use client'

import { useState } from 'react'
import { MessageWithAuthor } from '@/types'
import { hapticFeedback } from '@/lib/haptics'

interface MobileThreadNavigationProps {
  message: MessageWithAuthor
  isExpanded: boolean
  onToggle: () => void
  onReply: () => void
  replyCount: number
}

export default function MobileThreadNavigation({
  message,
  isExpanded,
  onToggle,
  onReply,
  replyCount
}: MobileThreadNavigationProps) {
  const [isPressed, setIsPressed] = useState(false)

  return (
    <div
      className="flex items-center justify-between mt-2 px-2 py-1 bg-gray-50 rounded-lg border border-gray-200 md:hidden"
      data-testid="mobile-thread-nav"
    >
      {/* Thread toggle */}
      {replyCount > 0 && (
        <button
          onClick={() => {
            hapticFeedback.medium()
            onToggle()
          }}
          className="flex items-center space-x-2 px-3 py-2 rounded-md bg-white border border-gray-200 hover:bg-gray-50 transition-colors"
        >
          <svg 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
            className={`transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
          >
            <path d="M9 18l6-6-6-6"/>
          </svg>
          <span className="text-sm font-medium text-gray-700">
            {replyCount} {replyCount === 1 ? 'reply' : 'replies'}
          </span>
        </button>
      )}

      {/* Reply button */}
      <button
        onClick={() => {
          hapticFeedback.light()
          onReply()
        }}
        onTouchStart={() => {
          setIsPressed(true)
          hapticFeedback.light()
        }}
        onTouchEnd={() => setIsPressed(false)}
        className={`
          flex items-center space-x-2 px-4 py-2 rounded-md
          bg-blue-500 hover:bg-blue-600 text-white
          transition-all duration-200
          active:scale-95
          ${isPressed ? 'scale-95 bg-blue-600' : ''}
        `}
      >
        <svg 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        >
          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
        </svg>
        <span className="text-sm font-medium">Reply</span>
      </button>
    </div>
  )
}

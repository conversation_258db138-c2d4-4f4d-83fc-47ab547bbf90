import { User, Group, Message, Note, NoteBlock, GroupMember, Role, BlockType } from '@prisma/client'

export type { User, Group, Message, Note, NoteBlock, GroupMember, Role, BlockType }

export interface UserWithGroups extends User {
  ownedGroups: Group[]
  memberships: (GroupMember & { group: Group })[]
}

export interface GroupWithMembers extends Group {
  owner: User
  members: (GroupMember & { user: User })[]
  _count: {
    members: number
    messages: number
    notes: number
  }
}

export interface MessageWithAuthor extends Message {
  author: User
  parentMessage?: MessageWithAuthor | null
  replies?: MessageWithAuthor[]
  _count?: {
    replies: number
  }
}

export interface NoteWithBlocks extends Note {
  author: User
  blocks: NoteBlockWithAuthor[]
  _count?: {
    blocks: number
  }
}

export interface NoteBlockWithAuthor extends NoteBlock {
  author: User
}

export interface CreateUserData {
  email: string
  username: string
  password: string
  name?: string
}

export interface CreateGroupData {
  name: string
  description?: string
  isPrivate?: boolean
}

export interface CreateMessageData {
  content: string
  groupId: string
  parentMessageId?: string
}

export interface CreateNoteData {
  title: string
  description?: string
  groupId: string
}

export interface CreateNoteBlockData {
  type: BlockType
  content: string
  noteId: string
  order: number
}

// Thread-related interfaces
export interface ThreadMessage extends MessageWithAuthor {
  isThreadRoot: boolean
  threadReplies: MessageWithAuthor[]
}

export interface CreateReplyData {
  content: string
  parentMessageId: string
  groupId: string
}

export interface ThreadDisplayOptions {
  maxDepth: number
  showCollapsed: boolean
  sortOrder: 'asc' | 'desc'
}

import { NextRequest } from 'next/server'
import { POST } from '@/app/api/notes/[noteId]/blocks/route'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { BlockType } from '@prisma/client'

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    note: {
      findFirst: jest.fn(),
    },
    noteBlock: {
      updateMany: jest.fn(),
      create: jest.fn(),
    },
  },
}))

jest.mock('@/lib/middleware', () => ({
  getAuthenticatedUser: jest.fn(),
}))

const mockPrisma = jest.mocked(prisma)
const mockGetAuthenticatedUser = jest.mocked(getAuthenticatedUser)

describe('/api/notes/[noteId]/blocks POST', () => {
  const mockUser = {
    id: 'user-1',
    username: 'testuser',
    email: '<EMAIL>',
    name: 'Test User',
    avatar: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  const mockNote = {
    id: 'note-1',
    title: 'Test Note',
    description: 'Test Description',
    authorId: 'user-1',
    groupId: 'group-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  const mockCreatedBlock = {
    id: 'block-1',
    type: BlockType.CODE,
    content: 'console.log("test")',
    order: 0,
    noteId: 'note-1',
    authorId: 'user-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    author: {
      id: 'user-1',
      username: 'testuser',
      name: 'Test User',
      avatar: null,
    },
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockGetAuthenticatedUser.mockResolvedValue(mockUser)
    mockPrisma.note.findFirst.mockResolvedValue(mockNote)
    mockPrisma.noteBlock.updateMany.mockResolvedValue({ count: 0 })
    mockPrisma.noteBlock.create.mockResolvedValue(mockCreatedBlock)
  })

  describe('Block Type Validation', () => {
    const testCases = [
      { type: BlockType.TEXT, description: 'TEXT block' },
      { type: BlockType.HEADING_1, description: 'HEADING_1 block' },
      { type: BlockType.HEADING_2, description: 'HEADING_2 block' },
      { type: BlockType.HEADING_3, description: 'HEADING_3 block' },
      { type: BlockType.BULLET_LIST, description: 'BULLET_LIST block' },
      { type: BlockType.NUMBERED_LIST, description: 'NUMBERED_LIST block' },
      { type: BlockType.CODE, description: 'CODE block' },
      { type: BlockType.QUOTE, description: 'QUOTE block' },
    ]

    testCases.forEach(({ type, description }) => {
      it(`should create ${description} successfully`, async () => {
        const requestBody = {
          type,
          content: `Test ${type} content`,
          order: 0,
        }

        const request = {
          json: jest.fn().mockResolvedValue(requestBody),
          url: 'http://localhost:3000/api/notes/note-1/blocks',
        } as any

        const params = Promise.resolve({ noteId: 'note-1' })
        const response = await POST(request, { params })
        const data = await response.json()

        console.log('Response status:', response.status)
        console.log('Response data:', data)
        expect(response.status).toBe(200)
        expect(data.message).toBe('Block created successfully')
        expect(data.block).toBeDefined()
        expect(mockPrisma.noteBlock.create).toHaveBeenCalledWith({
          data: {
            type,
            content: `Test ${type} content`,
            order: 0,
            noteId: 'note-1',
            authorId: 'user-1',
          },
          include: {
            author: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              },
            },
          },
        })
      })
    })
  })

  it('should reject invalid block type', async () => {
    const request = new NextRequest('http://localhost:3000/api/notes/note-1/blocks', {
      method: 'POST',
      body: JSON.stringify({
        type: 'INVALID_TYPE',
        content: 'Test content',
        order: 0,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const params = Promise.resolve({ noteId: 'note-1' })
    const response = await POST(request, { params })
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Validation error')
    expect(data.details).toBeDefined()
  })

  it('should handle empty content', async () => {
    const request = new NextRequest('http://localhost:3000/api/notes/note-1/blocks', {
      method: 'POST',
      body: JSON.stringify({
        type: 'TEXT',
        content: '',
        order: 0,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const params = Promise.resolve({ noteId: 'note-1' })
    const response = await POST(request, { params })
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.message).toBe('Block created successfully')
  })

  it('should require authentication', async () => {
    mockGetAuthenticatedUser.mockResolvedValue(null)

    const request = new NextRequest('http://localhost:3000/api/notes/note-1/blocks', {
      method: 'POST',
      body: JSON.stringify({
        type: 'TEXT',
        content: 'Test content',
        order: 0,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const params = Promise.resolve({ noteId: 'note-1' })
    const response = await POST(request, { params })
    const data = await response.json()

    expect(response.status).toBe(401)
    expect(data.error).toBe('Authentication required')
  })

  it('should handle note not found', async () => {
    mockPrisma.note.findFirst.mockResolvedValue(null)

    const request = new NextRequest('http://localhost:3000/api/notes/note-1/blocks', {
      method: 'POST',
      body: JSON.stringify({
        type: 'TEXT',
        content: 'Test content',
        order: 0,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const params = Promise.resolve({ noteId: 'note-1' })
    const response = await POST(request, { params })
    const data = await response.json()

    expect(response.status).toBe(404)
    expect(data.error).toBe('Note not found or access denied')
  })
})

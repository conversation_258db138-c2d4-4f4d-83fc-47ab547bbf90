# Dokumentasi Sistem Grup Management

## Overview

Sistem grup management telah diperluas dengan fitur-fitur baru untuk mendukung operasi grup yang lebih komprehensif, termasuk delete group, join/leave group, transfer ownership, dan role management yang lebih robust.

## Database Schema

### Model Group
```prisma
model Group {
  id          String        @id @default(cuid())
  name        String
  description String?
  avatar      String?
  isPrivate   Boolean       @default(false)  // Perbedaan public vs private
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  ownerId     String
  members     GroupMember[]
  owner       User          @relation("GroupOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  messages    Message[]
  notes       Note[]
}
```

### Model GroupMember
```prisma
model GroupMember {
  id       String   @id @default(cuid())
  role     Role     @default(MEMBER)  // OWNER, ADMIN, MEMBER
  joinedAt DateTime @default(now())
  userId   String
  groupId  String
  group    Group    @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

### Enum Role
```prisma
enum Role {
  OWNER   // Dapat menghapus grup, transfer ownership, manage semua anggota
  ADMIN   // Dapat manage anggota (invite, kick), manage pesan
  MEMBER  // Dapat mengirim pesan, melihat anggota
}
```

## API Endpoints

### 1. Delete Group
**Endpoint:** `DELETE /api/groups/[groupId]`

**Deskripsi:** Menghapus grup beserta semua data terkait (cascade deletion)

**Autorisasi:** Hanya Owner yang dapat menghapus grup

**Response Success (200):**
```json
{
  "message": "Group deleted successfully",
  "deletedGroup": {
    "id": "group_id",
    "name": "Group Name",
    "memberCount": 5,
    "messageCount": 100,
    "noteCount": 10
  }
}
```

**Response Error (404):**
```json
{
  "error": "Group not found or insufficient permissions. Only group owners can delete groups."
}
```

### 2. Join Group
**Endpoint:** `POST /api/groups/[groupId]/join`

**Deskripsi:** Bergabung dengan grup (hanya untuk public groups)

**Business Logic:**
- Public groups: Join langsung tanpa invite
- Private groups: Memerlukan invite (ditolak dengan error 403)
- Tidak bisa join jika sudah menjadi member

**Response Success (200):**
```json
{
  "message": "Successfully joined the group",
  "member": {
    "id": "member_id",
    "role": "MEMBER",
    "user": { "id": "user_id", "username": "username" },
    "group": { "id": "group_id", "name": "Group Name" }
  }
}
```

**Response Error (403) - Private Group:**
```json
{
  "error": "This is a private group. You need an invitation to join.",
  "groupInfo": {
    "id": "group_id",
    "name": "Group Name",
    "isPrivate": true,
    "memberCount": 5
  }
}
```

### 3. Leave Group
**Endpoint:** `POST /api/groups/[groupId]/leave`

**Deskripsi:** Keluar dari grup

**Business Logic:**
- Owner tidak bisa leave jika masih ada member lain
- Owner harus transfer ownership atau promote admin terlebih dahulu
- Member dan Admin bisa leave kapan saja

**Response Success (200):**
```json
{
  "message": "Successfully left the group",
  "leftGroup": {
    "id": "group_id",
    "name": "Group Name",
    "formerRole": "MEMBER",
    "remainingMembers": 4
  }
}
```

**Response Error (400) - Owner dengan Members:**
```json
{
  "error": "As the group owner, you cannot leave while there are other members. Please transfer ownership to an admin first, or delete the group.",
  "suggestion": "Promote another member to admin first, then transfer ownership before leaving.",
  "memberCount": 5
}
```

### 4. Transfer Ownership
**Endpoint:** `POST /api/groups/[groupId]/transfer-ownership`

**Deskripsi:** Transfer ownership grup ke member lain

**Request Body:**
```json
{
  "newOwnerId": "user_id"
}
```

**Business Logic:**
- Hanya Owner yang bisa transfer ownership
- Target harus sudah menjadi member grup
- Owner lama menjadi Admin setelah transfer

**Response Success (200):**
```json
{
  "message": "Ownership transferred successfully",
  "transfer": {
    "groupId": "group_id",
    "groupName": "Group Name",
    "formerOwner": {
      "id": "former_owner_id",
      "username": "former_owner"
    },
    "newOwner": {
      "id": "new_owner_id",
      "username": "new_owner"
    }
  }
}
```

### 5. Update Member Role
**Endpoint:** `PUT /api/groups/[groupId]/members/[memberId]/role`

**Deskripsi:** Mengubah role member (promote/demote)

**Request Body:**
```json
{
  "role": "ADMIN" // atau "MEMBER"
}
```

**Business Logic:**
- Owner dapat promote/demote siapa saja
- Admin hanya bisa manage Member (tidak bisa promote ke Admin atau demote Admin lain)
- Tidak bisa mengubah role Owner
- Tidak bisa mengubah role sendiri (kecuali Owner)

**Response Success (200):**
```json
{
  "message": "Member role updated successfully",
  "member": {
    "id": "member_id",
    "user": { "id": "user_id", "username": "username" },
    "role": "ADMIN",
    "previousRole": "MEMBER"
  }
}
```

### 6. Remove Member
**Endpoint:** `DELETE /api/groups/[groupId]/members?memberId=member_id`

**Deskripsi:** Mengeluarkan member dari grup

**Business Logic:**
- Owner dan Admin bisa remove Member
- Hanya Owner yang bisa remove Admin
- Tidak bisa remove Owner
- Tidak bisa remove diri sendiri (gunakan leave endpoint)

**Response Success (200):**
```json
{
  "message": "Member removed successfully",
  "removedMember": {
    "id": "member_id",
    "user": { "id": "user_id", "username": "username" },
    "role": "MEMBER"
  }
}
```

### 7. Discover Public Groups
**Endpoint:** `GET /api/groups/discover?page=1&limit=20&search=keyword`

**Deskripsi:** Mencari public groups yang bisa di-join

**Query Parameters:**
- `page`: Halaman (default: 1)
- `limit`: Jumlah per halaman (default: 20)
- `search`: Keyword pencarian (optional)

**Response Success (200):**
```json
{
  "groups": [
    {
      "id": "group_id",
      "name": "Public Group",
      "description": "Description",
      "isPrivate": false,
      "memberCount": 10,
      "messageCount": 50,
      "noteCount": 5,
      "owner": { "id": "owner_id", "username": "owner" },
      "canJoin": true
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "totalCount": 100,
    "totalPages": 5,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

## Permission Matrix

| Action | Owner | Admin | Member |
|--------|-------|-------|--------|
| Delete Group | ✅ | ❌ | ❌ |
| Update Group Info | ✅ | ✅ | ❌ |
| Transfer Ownership | ✅ | ❌ | ❌ |
| Promote to Admin | ✅ | ❌ | ❌ |
| Demote Admin | ✅ | ❌ | ❌ |
| Manage Members | ✅ | ✅* | ❌ |
| Remove Admin | ✅ | ❌ | ❌ |
| Remove Member | ✅ | ✅ | ❌ |
| Leave Group | ✅** | ✅ | ✅ |
| Send Messages | ✅ | ✅ | ✅ |
| Create Notes | ✅ | ✅ | ✅ |

*Admin hanya bisa manage Member, tidak bisa manage Admin lain
**Owner tidak bisa leave jika masih ada member lain

## Public vs Private Groups

### Public Groups
- Dapat ditemukan melalui `/api/groups/discover`
- User dapat join langsung tanpa invite
- Terlihat dalam pencarian grup
- Cocok untuk komunitas terbuka

### Private Groups
- Tidak muncul dalam discover
- Memerlukan invite untuk bergabung
- Hanya member yang bisa melihat grup
- Cocok untuk tim atau grup tertutup

## Business Logic Flow

### Join Group Flow
```mermaid
graph TD
    A[User Request Join] --> B{Group Exists?}
    B -->|No| C[404 Not Found]
    B -->|Yes| D{Already Member?}
    D -->|Yes| E[400 Already Member]
    D -->|No| F{Is Private?}
    F -->|Yes| G[403 Need Invitation]
    F -->|No| H[Create Membership]
    H --> I[200 Success]
```

### Leave Group Flow
```mermaid
graph TD
    A[User Request Leave] --> B{Is Member?}
    B -->|No| C[404 Not Member]
    B -->|Yes| D{Is Owner?}
    D -->|No| E[Remove Membership]
    D -->|Yes| F{Has Other Members?}
    F -->|No| G[Allow Leave]
    F -->|Yes| H{Has Admins?}
    H -->|No| I[400 Promote Admin First]
    H -->|Yes| J[400 Transfer Ownership]
    E --> K[200 Success]
    G --> K
```

## Error Handling

Semua endpoint menggunakan consistent error format:

```json
{
  "error": "Error message",
  "details": "Additional details (optional)"
}
```

Common HTTP Status Codes:
- `200`: Success
- `400`: Bad Request (validation error, business logic violation)
- `401`: Unauthorized (authentication required)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error

## Logging

Semua operasi penting di-log untuk audit purposes:
- Group deletion
- Ownership transfer
- Member join/leave
- Role changes
- Member removal

Log format: `User {userId} ({username}) performed {action} on group {groupId} ({groupName})`

## Security Considerations

1. **Authentication**: Semua endpoint memerlukan valid JWT token
2. **Authorization**: Role-based access control untuk setiap operasi
3. **Input Validation**: Menggunakan Zod schema untuk validasi input
4. **SQL Injection Prevention**: Menggunakan Prisma ORM
5. **Cascade Deletion**: Otomatis menghapus data terkait saat grup dihapus
6. **Audit Trail**: Logging untuk tracking perubahan penting

## Testing

Unit tests telah dibuat untuk semua endpoint baru:
- Join group scenarios (public/private, already member)
- Leave group scenarios (member/owner, with/without other members)
- Transfer ownership (valid/invalid targets)
- Delete group (owner/non-owner permissions)
- Role management (various permission levels)

Untuk menjalankan tests:
```bash
npm test src/__tests__/api/groups/group-management.test.ts
```

import { test, expect } from '@playwright/test';

test.describe('Basic App Functionality', () => {
  test('should load the homepage', async ({ page }) => {
    await page.goto('/');
    
    // Check if the page loads
    await expect(page).toHaveTitle(/MyBinder/);
  });

  test('should navigate to auth page', async ({ page }) => {
    await page.goto('/auth');
    
    // Check if auth page loads
    await expect(page.locator('h1')).toContainText(/Sign/);
  });

  test('should show mobile-responsive design', async ({ page, isMobile }) => {
    await page.goto('/');
    
    if (isMobile) {
      // Check mobile-specific elements
      await expect(page.locator('body')).toHaveClass(/mobile/);
    } else {
      // Check desktop layout
      await expect(page.locator('body')).not.toHaveClass(/mobile/);
    }
  });
});
